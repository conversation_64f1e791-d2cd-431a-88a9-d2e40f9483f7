"use client";
import React, { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>Content,
  <PERSON>alogD<PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>Trigger,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import {
  <PERSON><PERSON><PERSON>t,
  CheckCircle2,
  <PERSON><PERSON>,
  <PERSON>2,
  <PERSON>ader2,
  Plus,
  UserPlus,
} from "lucide-react";
import { DialogClose } from "@radix-ui/react-dialog";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { toast } from "@/components/ui/use-toast";
import { useRouter } from "next/navigation";
import { availableNetworks } from "@/constants/availableNetworks";
import { cn } from "@/lib/utils";
import { createConnection } from "@/lib/actions/createConnection";
import { platformConnections } from "@repo/api/src/db/schema";

const CreateConnectionButton = ({ projectId }: { projectId: string }) => {
  const router = useRouter();

  const [isCreateConnectionOpen, setIsCreateConnectionOpen] = useState(false);
  const [newConnectionName, setNewConnectionName] = useState("");
  const [selectedPlatform, setSelectedPlatform] = useState<
    (typeof availableNetworks)[number] | null
  >(null);

  // New state for multi-step dialog
  const [currentStep, setCurrentStep] = useState(1); // 1: Choose method, 2: Configure
  const [connectionMethod, setConnectionMethod] = useState<
    "direct" | "link" | null
  >(null);
  const [generatedLink, setGeneratedLink] = useState<string | null>(null);
  const [isGeneratingLink, setIsGeneratingLink] = useState(false);
  const [isCopied, setIsCopied] = useState(false);

  const resetDialogState = () => {
    setNewConnectionName("");
    setSelectedPlatform(null);
    setCurrentStep(1);
    setConnectionMethod(null);
    setGeneratedLink(null);
    setIsGeneratingLink(false);
    setIsCopied(false);
  };

  const handleDialogClose = (isOpen: boolean) => {
    setIsCreateConnectionOpen(isOpen);
    if (!isOpen) {
      resetDialogState();
    }
  };

  const handleMethodSelect = (method: "direct" | "link") => {
    setConnectionMethod(method);
    setCurrentStep(2);
  };

  const handleCreateDirectConnection = async () => {
    if (!newConnectionName || !selectedPlatform) {
      toast({
        title: "Missing Information",
        description: "Please provide a connection name and select a platform.",
        variant: "destructive",
      });
      return;
    }

    setIsGeneratingLink(true);
    const newConnection = await createConnection({
      name: newConnectionName,
      projectId: projectId,
      platform:
        selectedPlatform.slug as (typeof platformConnections.platform.enumValues)[number],
      generateLink: false,
    });

    setIsGeneratingLink(false);

    toast({
      title: "Connection created",
      description: `New ${selectedPlatform.name} connection "${newConnectionName}" has been initiated.`,
    });
    // In a real app, you'd refetch connections or update state
    // For now, let's navigate to the new connection's page (mock)
    router.push(
      `/dashboard/projects/${projectId}/connections/${newConnection.id}/connect`
    );
    handleDialogClose(false); // Close dialog
  };

  const handleGenerateShareableLink = async () => {
    if (!newConnectionName || !selectedPlatform) {
      toast({
        title: "Missing Information",
        description: "Please provide a connection name and select a platform.",
        variant: "destructive",
      });
      return;
    }
    setIsGeneratingLink(true);
    setGeneratedLink(null); // Clear previous link

    const newConnection = await createConnection({
      name: newConnectionName,
      projectId: projectId,
      platform:
        selectedPlatform.slug as (typeof platformConnections.platform.enumValues)[number],
      generateLink: true,
    });

    const fullLink = `${window.location.origin}/connect/${newConnection.link}`;

    setGeneratedLink(fullLink);
    setIsGeneratingLink(false);
    toast({
      title: "Link Generated",
      description: "Shareable link has been created.",
    });
  };

  const handleCopyToClipboard = () => {
    if (!generatedLink) return;
    navigator.clipboard
      .writeText(generatedLink)
      .then(() => {
        setIsCopied(true);
        toast({ title: "Copied to clipboard!" });
        setTimeout(() => setIsCopied(false), 2000);
      })
      .catch((err) => {
        toast({
          title: "Failed to copy",
          description: "Could not copy link to clipboard.",
          variant: "destructive",
        });
      });
  };

  const renderStep1 = () => (
    <>
      <DialogHeader>
        <DialogTitle>Create New Connection</DialogTitle>
        <DialogDescription>
          How would you like to connect this new social media account?
        </DialogDescription>
      </DialogHeader>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 py-6">
        <button
          onClick={() => handleMethodSelect("direct")}
          className="flex flex-col items-center justify-center p-6 rounded-lg border-2 border-border hover:border-primary hover:bg-muted transition-all text-center"
        >
          <UserPlus className="h-10 w-10 mb-3 text-primary" />
          <h3 className="text-lg font-semibold">Connect Myself</h3>
          <p className="text-sm text-muted-foreground mt-1">
            Authorize the connection right now using your credentials.
          </p>
        </button>
        <button
          onClick={() => handleMethodSelect("link")}
          className="flex flex-col items-center justify-center p-6 rounded-lg border-2 border-border hover:border-primary hover:bg-muted transition-all text-center"
        >
          <Link2 className="h-10 w-10 mb-3 text-primary" />
          <h3 className="text-lg font-semibold">Generate Shareable Link</h3>
          <p className="text-sm text-muted-foreground mt-1">
            Create a link for someone else to complete the connection.
          </p>
        </button>
      </div>
      <DialogFooter>
        <DialogClose asChild>
          <Button variant="outline">Cancel</Button>
        </DialogClose>
      </DialogFooter>
    </>
  );

  const renderStep2 = () => (
    <>
      <DialogHeader>
        <div className="flex items-center mb-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => {
              setCurrentStep(1);
              setGeneratedLink(null);
            }}
            className="mr-2"
          >
            <ArrowLeft className="h-4 w-4 mr-1" /> Back
          </Button>
          <DialogTitle>
            {connectionMethod === "direct"
              ? "Configure New Connection"
              : "Configure for Shareable Link"}
          </DialogTitle>
        </div>
        <DialogDescription>
          {connectionMethod === "direct"
            ? "Provide a name and select the platform for your new connection."
            : "Provide a name and select the platform. A link will be generated for this setup."}
        </DialogDescription>
      </DialogHeader>
      <div className="grid gap-6 py-4">
        <div className="grid gap-2">
          <Label htmlFor="connection-name">Connection Name</Label>
          <Input
            id="connection-name"
            placeholder="e.g., Company Instagram Account"
            value={newConnectionName}
            onChange={(e) => setNewConnectionName(e.target.value)}
            disabled={isGeneratingLink || !!generatedLink}
          />
        </div>
        <div className="grid gap-2">
          <Label>Select Platform</Label>
          <div className="grid grid-cols-2 sm:grid-cols-3 gap-3">
            {availableNetworks.map((platform) => {
              const IconComponent = platform.icon;
              return (
                <button
                  key={platform.slug}
                  type="button"
                  onClick={() => setSelectedPlatform(platform)}
                  disabled={isGeneratingLink || !!generatedLink}
                  className={cn(
                    "flex flex-col items-center justify-center p-4 rounded-lg border-2 transition-all duration-150 ease-in-out",
                    "focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary",
                    selectedPlatform?.slug === platform.slug
                      ? "border-primary ring-2 ring-primary shadow-lg"
                      : "border-border hover:border-primary/70 hover:shadow-md",
                    (isGeneratingLink || !!generatedLink) &&
                      "opacity-70 cursor-not-allowed"
                  )}
                >
                  <IconComponent
                    className={cn(
                      "h-8 w-8 mb-2",
                      selectedPlatform?.slug === platform.slug
                        ? "text-primary"
                        : "text-muted-foreground"
                    )}
                  />
                  <span
                    className={cn(
                      "text-sm font-medium",
                      selectedPlatform?.slug === platform.slug
                        ? "text-primary"
                        : "text-foreground"
                    )}
                  >
                    {platform.name}
                  </span>
                </button>
              );
            })}
          </div>
          {!selectedPlatform && connectionMethod === "direct" && (
            <p className="text-xs text-red-500">Please select a platform.</p>
          )}
        </div>

        {connectionMethod === "link" && generatedLink && (
          <div className="space-y-2 pt-4 border-t">
            <Label className="font-semibold">Shareable Link:</Label>
            <div className="flex items-center space-x-2">
              <Input
                type="text"
                readOnly
                value={generatedLink}
                className="bg-muted"
              />
              <Button
                variant="outline"
                size="icon"
                onClick={handleCopyToClipboard}
                disabled={isCopied}
              >
                {isCopied ? (
                  <CheckCircle2 className="h-4 w-4 text-green-500" />
                ) : (
                  <Copy className="h-4 w-4" />
                )}
              </Button>
            </div>
            <p className="text-xs text-muted-foreground">
              This link is valid for 48 hours (mock duration).
            </p>
          </div>
        )}
      </div>
      <DialogFooter>
        <DialogClose asChild>
          <Button
            variant="outline"
            onClick={() => {
              if (generatedLink)
                resetDialogState(); /* else do nothing, let DialogClose handle */
            }}
          >
            {generatedLink ? "Done" : "Cancel"}
          </Button>
        </DialogClose>
        {connectionMethod === "direct" && (
          <Button
            onClick={handleCreateDirectConnection}
            disabled={!newConnectionName || !selectedPlatform}
          >
            Create & Connect
          </Button>
        )}
        {connectionMethod === "link" && !generatedLink && (
          <Button
            onClick={handleGenerateShareableLink}
            disabled={
              !newConnectionName || !selectedPlatform || isGeneratingLink
            }
          >
            {isGeneratingLink && (
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            )}
            Generate Link
          </Button>
        )}
      </DialogFooter>
    </>
  );

  return (
    <Dialog open={isCreateConnectionOpen} onOpenChange={handleDialogClose}>
      <DialogTrigger asChild>
        <Button>
          <Plus className="h-4 w-4 mr-2" />
          Add Connection
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-lg">
        {currentStep === 1 ? renderStep1() : renderStep2()}
      </DialogContent>
    </Dialog>
  );
};

export default CreateConnectionButton;
