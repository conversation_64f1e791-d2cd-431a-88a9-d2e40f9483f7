{"name": "socialfeed", "private": true, "scripts": {"build": "turbo run build", "deploy": "turbo run deploy", "dev": "turbo run dev", "lint": "turbo run lint", "format": "prettier --write \"**/*.{ts,tsx,md}\"", "check-types": "turbo run check-types", "db:generate": "turbo run db:generate --filter=@repo/api", "db:migrate:local": "turbo run db:migrate:local --filter=@repo/api", "db:migrate:prod": "turbo run db:migrate:prod --filter=@repo/api"}, "devDependencies": {"prettier": "^3.6.2", "turbo": "^2.5.5", "typescript": "5.8.2"}, "packageManager": "pnpm@10.13.1", "engines": {"node": ">=18"}}