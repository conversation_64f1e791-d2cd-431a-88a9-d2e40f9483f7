"use client";
import React from "react";
import { useState } from "react";
import { AlertCircle } from "lucide-react";
import {
  <PERSON>,
  CardContent,
  CardDescription,
  <PERSON><PERSON>ooter,
  Card<PERSON>eader,
  CardTitle,
} from "@/components/ui/card";
import { Switch } from "@/components/ui/switch";
import { toast } from "@/hooks/use-toast";
import StatusBadge from "@/components/primitives/StatusBadge";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { availableNetworks } from "@/constants/availableNetworks";
import { updateFeed } from "@/lib/actions/updateFeed";
import { type platformConnections } from "@repo/drizzle-schema/d1";

const ConnectionFeedSettings = ({
  feedConnections,
  availableConnections,
  projectId,
  feedId,
}: {
  projectId: string;
  feedId: string;
  feedConnections: {
    id: string;
    name: string;
    platform: (typeof platformConnections.$inferSelect)["platform"];
    createdAt: Date | null;
    isActive: boolean;
    hasError: boolean;
    isConnected: boolean;
    tokenExpiresAt: Date | null;
    lastPolledAt: Date | null;
    platformAccountName: string | null;
    lastCheckedAt: Date | null;
    status: "active" | "inactive" | "auth_needed" | "expired";
  }[];
  availableConnections: {
    id: string;
    name: string;
    platformAccountName: string | null;
    lastPolledAt: string | null;
    projectId: string;
    platform: "instagram" | "instagram_business" | "facebook" | "tiktok";
    status: "active" | "inactive" | "auth_needed" | "expired";
    createdAt: string | null;
  }[];
}) => {
  const [selectedConnections, setSelectedConnections] = useState<string[]>(
    feedConnections.map((conn) => conn.id)
  );

  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);

  const handleSaveSettings = async () => {
    await updateFeed({
      connections: selectedConnections,
      projectId: projectId,
      feedId: feedId,
    });
    setHasUnsavedChanges(false);
    toast({
      title: "Feed updated",
      description: "Feed configuration has been saved successfully.",
    });
  };

  const handleConnectionToggle = (connectionId: string) => {
    setSelectedConnections((prev) => {
      const newConnections = prev.includes(connectionId)
        ? prev.filter((id) => id !== connectionId)
        : [...prev, connectionId];
      setHasUnsavedChanges(true);
      return newConnections;
    });
  };
  return (
    <Card>
      <CardHeader>
        <CardTitle>Data Sources</CardTitle>
        <CardDescription>
          Select which connections this feed should aggregate data from.
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {availableConnections.map((connection) => {
            const IconComponent =
              availableNetworks.find((n) => n.slug === connection.platform)
                ?.icon || AlertCircle;
            const isSelected = selectedConnections.includes(connection.id);

            const isDisabled =
              selectedConnections.length > 0 &&
              connection.platform !==
                availableConnections.find(
                  (ac) => ac.id === selectedConnections[0]
                )?.platform;
            return (
              <div
                key={connection.id}
                className={cn(
                  "flex items-center justify-between p-4 border rounded-lg",
                  isDisabled && "text-muted-foreground"
                )}
              >
                <div className="flex items-center space-x-4">
                  <IconComponent className="h-8 w-8" />
                  <div>
                    <h3 className="font-medium">{connection.name}</h3>
                    <p className="text-sm text-muted-foreground flex gap-2">
                      {connection.platform}
                      <span>•</span>
                      <StatusBadge
                        status={connection.status}
                        className={cn(isDisabled && "opacity-50")}
                      />
                    </p>
                  </div>
                </div>
                <Switch
                  checked={isSelected}
                  onCheckedChange={() => handleConnectionToggle(connection.id)}
                  disabled={isDisabled}
                />
              </div>
            );
          })}
        </div>
      </CardContent>
      <CardFooter>
        <Button disabled={!hasUnsavedChanges} onClick={handleSaveSettings}>
          Save
        </Button>
      </CardFooter>
    </Card>
  );
};

export default ConnectionFeedSettings;
