import { Context } from "hono";
import { AppContext } from "../../../types";
import { HTTPException } from "hono/http-exception";
import { getDbClient } from "../../../database-service";
import * as schema from "@repo/drizzle-schema/d1";
import { logErrorToAnalytics } from "../../../analytics-utils";
import { fixUserAndOrgTable } from "../../manage/account/fix";
import { zValidator } from "@hono/zod-validator";
import { z } from "zod";

const validator = z.object({
  name: z.string(),
  description: z.string().optional().nullable(),
});

export const createProjectValidator = zValidator("json", validator);

export const createProject = async (
  c: Context<
    AppContext,
    "/manage/projects",
    {
      in: {
        json: z.infer<typeof validator>;
      };
      out: {
        json: z.infer<typeof validator>;
      };
    }
  >
) => {
  const organizationId = c.var.organizationId;
  if (!organizationId)
    throw new HTTPException(403, {
      message: "Organization context required",
    });

  const validated = c.req.valid("json");
  const feedName = validated.name;
  const feedDescription = validated.description;
  const userId = c.var.user?.userId;

  if (!feedName || !userId)
    throw new HTTPException(400, { message: "Project name required" });

  const db = getDbClient(c.env.DB);
  try {
    const resp = await db
      .insert(schema.projects)
      .values({
        createdBy: c.var.user?.userId,
        name: feedName,
        organizationId: organizationId,
        description: feedDescription,
      })
      .run();
    return c.json({ id: resp.success ? resp.results[0] : null });
  } catch (e) {
    console.error(`Failed to add feed for org ${organizationId}:`, e);
    logErrorToAnalytics(c.env, "FEED_ADD_ERROR", `Failed to add feed`, {
      organizationId,
      error: String(e),
    });
    await fixUserAndOrgTable({ user: c.var.user, d1: c.env.DB });
    throw new HTTPException(500, {
      message: "Failed to add feed",
    });
  }
};
