"use client";
import React, { useState } from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  <PERSON>alogDescription,
  <PERSON><PERSON>Footer,
  <PERSON><PERSON>Header,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { ChevronDownIcon, Plus } from "lucide-react";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { toast } from "@/components/ui/use-toast";
import { createLink } from "@/lib/actions/createLink";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Calendar } from "@/components/ui/calendar";

const CreateLinkButton = ({
  projectId,
  connectionId,
}: {
  projectId: string;
  connectionId: string;
}) => {
  const [isCreateOAuthLinkOpen, setIsCreateOAuthLinkOpen] = useState(false);
  const [name, setNewOAuthLinkName] = useState("");
  const [expires, setNewOAuthLinkExpiry] = useState<Date>();
  const [open, setOpen] = useState(false);

  const handleCreateOAuthLink = async () => {
    if (!name || !expires) {
      toast({
        title: "Missing fields",
        description: "Please provide a name and expiry date.",
        variant: "destructive",
      });
      return;
    }

    const creationToast = toast({
      title: "Creating OAuth Link",
      description: `Link "${name}" is being generated...`,
      duration: 0,
    });

    try {
      await createLink({ name, expires, projectId, connectionId });
    } catch (e) {
      creationToast.update({
        id: creationToast.id,
        title: "Error",
        description: "Failed to create link.",
        variant: "destructive",
        duration: 5000,
      });
      return;
    }

    creationToast.update({
      id: creationToast.id,
      title: "OAuth Link Created",
      description: `Link "${name}" generated.`,
      duration: 5000,
    });
    setIsCreateOAuthLinkOpen(false);
    setNewOAuthLinkName("");
    setNewOAuthLinkExpiry(undefined);
    setOpen(false);
  };

  return (
    <Dialog
      open={isCreateOAuthLinkOpen}
      onOpenChange={setIsCreateOAuthLinkOpen}
    >
      <DialogTrigger asChild>
        <Button>
          <Plus className="h-4 w-4 mr-2" /> Create Link
        </Button>
      </DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Create New OAuth Link</DialogTitle>
          <DialogDescription>
            Generate a new link with a name and expiry date.
          </DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="grid gap-2">
            <Label htmlFor="oauth-link-name">Link Name</Label>
            <Input
              id="oauth-link-name"
              placeholder="e.g., Marketing Team Q3"
              value={name}
              onChange={(e) => setNewOAuthLinkName(e.target.value)}
            />
          </div>
          <div className="flex gap-4">
            <div className="flex flex-col gap-3">
              <Label htmlFor="date-picker" className="px-1">
                Expiry Date
              </Label>
              <Popover open={open} onOpenChange={setOpen}>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    id="date-picker"
                    className="w-32 justify-between font-normal"
                  >
                    {expires ? expires.toLocaleDateString() : "Select date"}
                    <ChevronDownIcon />
                  </Button>
                </PopoverTrigger>
                <PopoverContent
                  className="w-auto overflow-hidden p-0"
                  align="start"
                  side="bottom"
                >
                  <Calendar
                    mode="single"
                    selected={expires}
                    captionLayout="dropdown"
                    disabled={{ before: new Date() }}
                    onSelect={(date) => {
                      setNewOAuthLinkExpiry(date);
                      setOpen(false);
                    }}
                  />
                </PopoverContent>
              </Popover>
            </div>
            <div className="flex flex-col gap-3">
              <Label htmlFor="time-picker" className="px-1">
                Time
              </Label>
              <Input
                type="time"
                id="time-picker"
                step="1"
                defaultValue="10:30:00"
                className="bg-background appearance-none [&::-webkit-calendar-picker-indicator]:hidden [&::-webkit-calendar-picker-indicator]:appearance-none"
              />
            </div>
          </div>
        </div>
        <DialogFooter>
          <Button
            variant="outline"
            onClick={() => setIsCreateOAuthLinkOpen(false)}
          >
            Cancel
          </Button>
          <Button onClick={handleCreateOAuthLink}>Create Link</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default CreateLinkButton;
