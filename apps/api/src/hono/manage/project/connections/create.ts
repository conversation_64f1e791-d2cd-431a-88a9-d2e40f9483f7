import { Context } from "hono";
import { AppContext } from "../../../../types";
import { HTTPException } from "hono/http-exception";
import { getDbClient } from "../../../../database-service";
import * as schema from "@repo/drizzle-schema/d1";
import { logErrorToAnalytics } from "../../../../analytics-utils";
import { fixUserAndOrgTable } from "../../../manage/account/fix";
import { secureLinkId } from "@repo/utils/nanoid";
import { DateTime } from "luxon";
import { zValidator } from "@hono/zod-validator";
import { z } from "zod";
import { and, eq } from "drizzle-orm";

const validator = z.object({
  name: z.string(),
  platform: z.enum(schema.platformConnections.platform.enumValues),
  generateLink: z.boolean().optional().nullable(),
});

export const createConnectionValidator = zValidator("json", validator);

export const createConnection = async (
  c: Context<
    AppContext,
    "/manage/projects/:projectId/connections",
    {
      in: {
        json: z.infer<typeof validator>;
      };
      out: {
        json: z.infer<typeof validator>;
      };
    }
  >
) => {
  const organizationId = c.var.organizationId;
  if (!organizationId)
    throw new HTTPException(403, {
      message: "Organization context required",
    });

  const validated = c.req.valid("json");

  const name = validated.name;
  const platform = validated.platform;
  const generateLink = validated.generateLink;
  const userId = c.var.user?.userId;

  if (!name || !userId)
    throw new HTTPException(400, { message: "Project name required" });

  const projectId = c.req.param("projectId");

  const linkToken = secureLinkId();

  const db = getDbClient(c.env.DB);
  try {
    const valid = await db
      .select({
        id: schema.projects.id,
      })
      .from(schema.projects)
      .where(
        and(
          eq(schema.projects.id, projectId),
          eq(schema.projects.organizationId, organizationId)
        )
      )
      .get();

    if (!valid) {
      throw new HTTPException(400, { message: "Project not found" });
    }

    const resp = await db
      .insert(schema.platformConnections)
      .values({
        projectId: projectId,
        name: name,
        platform: platform,
      })
      .returning({ id: schema.platformConnections.id })
      .run();
    if (resp.success && generateLink) {
      const linkData = await db
        .insert(schema.generatedLinks)
        .values({
          platformConnectionId: (resp.results[0] as { id: string }).id,
          name: "Automatically generated link",
          createdBy: userId,
          expiresAt: DateTime.now().plus({ days: 2 }).toJSDate(), // 2 Tage
          token: linkToken,
        })
        .run();

      return c.json({
        id: linkData.success
          ? ((resp.results[0] as { id: string })?.id ?? null)
          : null,
        link: linkToken,
      });
    }

    return c.json({
      id: resp.success
        ? ((resp.results[0] as { id: string })?.id ?? null)
        : null,
      link: 1 !== 1 ? linkToken : null,
    });
  } catch (e) {
    console.error(
      `Failed to add connection to project ${projectId} for org ${organizationId}:`,
      e
    );
    logErrorToAnalytics(
      c.env,
      "CONNECTION_ADD_ERROR",
      `Failed to add connection`,
      {
        projectId,
        organizationId,
        error: String(e),
      }
    );
    await fixUserAndOrgTable({ user: c.var.user, d1: c.env.DB });
    throw new HTTPException(500, {
      message: "Failed to add connection",
    });
  }
};
