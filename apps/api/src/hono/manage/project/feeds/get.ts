import { Context } from "hono";
import { AppContext } from "../../../../types";
import { BlankInput } from "hono/types";
import { HTTPException } from "hono/http-exception";
import { getDbClient } from "../../../../database-service";
import { logErrorToAnalytics } from "../../../../analytics-utils";
import * as schema from "@repo/drizzle-schema/d1";
import { and, eq, sql } from "drizzle-orm";
import { getConnectionStatus } from "../../../../utils/connectionStatus";

export const getOneFeed = async (
  c: Context<
    AppContext,
    "/manage/projects/:projectId/feeds/:feedId",
    BlankInput
  >
) => {
  const requestedProjectId = c.req.param("projectId");
  if (!requestedProjectId)
    throw new HTTPException(403, {
      message: "Project ID required",
    });

  const requestedFeedId = c.req.param("feedId");
  if (!requestedFeedId)
    throw new HTTPException(403, {
      message: "Feed ID required",
    });

  const organizationId = c.var.organizationId;
  if (!organizationId)
    throw new HTTPException(403, {
      message: "Organization context required",
    });

  const db = getDbClient(c.env.DB);

  try {
    const validation = await db
      .select({
        id: schema.projects.id,
      })
      .from(schema.projects)
      .leftJoin(schema.feeds, eq(schema.feeds.projectId, schema.projects.id))
      .where(
        and(
          eq(schema.feeds.id, requestedFeedId),
          eq(schema.feeds.projectId, requestedProjectId),
          eq(schema.projects.organizationId, organizationId)
        )
      )
      .get();

    if (!validation) {
      throw new HTTPException(404, {
        message: "Feed not found",
      });
    }
    const data = await db.query.feeds.findFirst({
      with: {
        connections: {
          with: {
            platformConnection: true,
          },
        },
        apiKeys: true,
        project: {
          columns: {
            organizationId: true,
          },
        },
      },
      where: (feedsTable, { eq, and }) => {
        return and(
          eq(feedsTable.id, requestedFeedId),
          eq(feedsTable.projectId, requestedProjectId)
        );
      },
    });

    if (!data)
      throw new HTTPException(404, {
        message: "Feed not found",
      });

    return c.json({
      ...data,
      connections: data.connections
        .map((conn) => conn.platformConnection)
        .map((conn) => ({
          id: conn.id,
          name: conn.name,
          platform: conn.platform,
          createdAt: conn.createdAt,
          isActive: conn.isActive,
          hasError: conn.hasError,
          isConnected: conn.isConnected,
          tokenExpiresAt: conn.tokenExpiresAt,
          lastPolledAt: conn.lastPolledAt,
          platformAccountName: conn.platformAccountName,
          lastCheckedAt: conn.lastCheckedAt,
          status: getConnectionStatus({
            isActive: conn.isActive,
            hasError: conn.hasError,
            tokenExpiresAt: conn.tokenExpiresAt,
            isConnected: conn.isConnected,
          }),
        })),
    });
  } catch (e) {
    console.error(
      `Failed to fetch feed ${requestedFeedId} for project ${requestedProjectId} for org ${organizationId}:`,
      e
    );
    logErrorToAnalytics(
      c.env,
      "PROJECTS_FEED_FETCH_ERROR",
      `Failed to fetch feed ${requestedFeedId} for project ${requestedProjectId} for org ${organizationId}`,
      {
        organizationId,
        error: String(e),
      }
    );
    throw new HTTPException(500, {
      message: "Failed to fetch projects feed",
    });
  }
};
