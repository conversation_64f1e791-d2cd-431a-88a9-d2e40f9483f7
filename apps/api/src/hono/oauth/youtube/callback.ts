// src/hono/oauth/youtube/callback.ts
import { Context } from "hono";
import { AppContext } from "../../../types";
import { HTTPException } from "hono/http-exception";
import { getDbClient } from "../../../database-service";
import * as schema from "@repo/drizzle-schema/d1";
import { logErrorToAnalytics } from "../../../analytics-utils";
import { encryptToken } from "../../../token-utils";
import { eq } from "drizzle-orm";
import { z } from "zod";

const YouTubeTokenResponseSchema = z.object({
  access_token: z.string(),
  refresh_token: z.string().optional(),
  expires_in: z.number(),
  scope: z.string(),
  token_type: z.string(),
});

const YouTubeChannelResponseSchema = z.object({
  items: z.array(z.object({
    id: z.string(),
    snippet: z.object({
      title: z.string(),
      customUrl: z.string().optional(),
    }),
  })),
});

export const youtubeCallbackHandler = async (c: Context<AppContext>) => {
  const code = c.req.query("code");
  const state = c.req.query("state");
  const error = c.req.query("error");

  if (error) {
    console.error("YouTube OAuth error:", error);
    return c.redirect(`${c.env.NEXT_PUBLIC_WEB_URL}/connect/youtube?error=${error}`);
  }

  if (!code || !state) {
    throw new HTTPException(400, { message: "Missing code or state parameter" });
  }

  try {
    // Decode state to get connection ID and redirect info
    const stateData = JSON.parse(decodeURIComponent(state));
    const { connectionId, token } = stateData;

    if (!connectionId) {
      throw new HTTPException(400, { message: "Invalid state parameter" });
    }

    // Exchange code for access token
    const tokenResponse = await fetch("https://oauth2.googleapis.com/token", {
      method: "POST",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
      body: new URLSearchParams({
        code,
        client_id: c.env.GOOGLE_CLIENT_ID,
        client_secret: c.env.GOOGLE_CLIENT_SECRET,
        redirect_uri: `${c.env.NEXT_PUBLIC_API_URL}/oauth/youtube/callback`,
        grant_type: "authorization_code",
      }),
    });

    if (!tokenResponse.ok) {
      const errorText = await tokenResponse.text();
      console.error("YouTube token exchange failed:", errorText);
      throw new HTTPException(400, { message: "Token exchange failed" });
    }

    const tokenData = await tokenResponse.json();
    const validation = YouTubeTokenResponseSchema.safeParse(tokenData);

    if (!validation.success) {
      console.error("Invalid token response:", validation.error);
      throw new HTTPException(400, { message: "Invalid token response" });
    }

    const { access_token, refresh_token, expires_in } = validation.data;

    // Get channel information
    const channelResponse = await fetch(
      `https://www.googleapis.com/youtube/v3/channels?part=snippet&mine=true&access_token=${access_token}`
    );

    if (!channelResponse.ok) {
      throw new HTTPException(400, { message: "Failed to fetch channel info" });
    }

    const channelData = await channelResponse.json();
    const channelValidation = YouTubeChannelResponseSchema.safeParse(channelData);

    if (!channelValidation.success || channelValidation.data.items.length === 0) {
      throw new HTTPException(400, { message: "No YouTube channel found" });
    }

    const channel = channelValidation.data.items[0];
    const channelId = channel.id;
    const channelTitle = channel.snippet.title;

    // Encrypt tokens
    const encryptedAccessToken = await encryptToken(access_token, c.env);
    const encryptedRefreshToken = refresh_token 
      ? await encryptToken(refresh_token, c.env) 
      : null;

    // Update connection in database
    const db = getDbClient(c.env.DB);
    const expiresAt = new Date(Date.now() + expires_in * 1000);

    await db
      .update(schema.platformConnections)
      .set({
        platformAccountId: channelId,
        platformAccountName: channelTitle,
        accessTokenEncrypted: encryptedAccessToken,
        refreshTokenEncrypted: encryptedRefreshToken,
        tokenExpiresAt: expiresAt,
        isConnected: true,
        isActive: true,
        hasError: false,
        needsReconnect: false,
        lastCheckedAt: new Date(),
      })
      .where(eq(schema.platformConnections.id, connectionId));

    // Subscribe to YouTube PubSubHubbub for this channel
    try {
      await subscribeToYouTubeChannel(channelId, c.env);
    } catch (subscribeError) {
      console.error("Failed to subscribe to YouTube channel:", subscribeError);
      // Don't fail the OAuth flow if subscription fails
      logErrorToAnalytics(
        c.env,
        "YOUTUBE_SUBSCRIPTION_ERROR",
        "Failed to subscribe to YouTube channel",
        { channelId, error: String(subscribeError) }
      );
    }

    // Redirect back to the connection page
    const redirectUrl = token 
      ? `${c.env.NEXT_PUBLIC_WEB_URL}/connect/youtube?token=${token}&success=true`
      : `${c.env.NEXT_PUBLIC_WEB_URL}/connect/youtube?success=true`;

    return c.redirect(redirectUrl);

  } catch (error: any) {
    console.error("YouTube OAuth callback error:", error);
    logErrorToAnalytics(
      c.env,
      "YOUTUBE_OAUTH_ERROR",
      "YouTube OAuth callback failed",
      { error: String(error) }
    );

    const redirectUrl = `${c.env.NEXT_PUBLIC_WEB_URL}/connect/youtube?error=oauth_failed`;
    return c.redirect(redirectUrl);
  }
};

/**
 * Subscribe to YouTube PubSubHubbub notifications for a channel
 */
async function subscribeToYouTubeChannel(channelId: string, env: any): Promise<void> {
  const hubUrl = "https://pubsubhubbub.appspot.com/subscribe";
  const topicUrl = `https://www.youtube.com/xml/feeds/videos.xml?channel_id=${channelId}`;
  const callbackUrl = `${env.NEXT_PUBLIC_API_URL}/webhooks/youtube`;

  const formData = new URLSearchParams({
    "hub.callback": callbackUrl,
    "hub.topic": topicUrl,
    "hub.verify": "async",
    "hub.mode": "subscribe",
    "hub.verify_token": env.YOUTUBE_VERIFY_TOKEN || "youtube_verify_token",
    "hub.lease_seconds": "864000", // 10 days
  });

  const response = await fetch(hubUrl, {
    method: "POST",
    headers: {
      "Content-Type": "application/x-www-form-urlencoded",
    },
    body: formData,
  });

  if (!response.ok) {
    const errorText = await response.text();
    throw new Error(`YouTube subscription failed: ${response.status} ${errorText}`);
  }

  console.log(`Successfully subscribed to YouTube channel: ${channelId}`);
}
