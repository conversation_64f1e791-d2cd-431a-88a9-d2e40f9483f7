// src/index.ts
import { Hono } from "hono";
import * as schema from "@repo/drizzle-schema/d1";

import * as constants from "./constants";

// Importiere alle Typen und Services
import {
  AppContext,
  Bindings,
  PaginationQueueMessage,
  SyncQueueMessage,
  PlatformConnection,
} from "./types";
import {
  getDbClient,
  updateConnectionStatus,
  upsertPost,
  getConnectionDetails,
  getActiveBasicDisplayConnections,
  getConnectionsForTokenCheck,
} from "./database-service";
import { DebouncerDO } from "./debouncer-do";
import { logErrorToAnalytics } from "./analytics-utils";
import { fetchNextCommentPage, GraphApiError } from "./graph-api-shared";
import { getPlatformAdapter } from "./platforms";
import { decryptToken } from "./token-utils";
import { timing } from "hono/timing";
import { secureHeaders } from "hono/secure-headers";
import { desc, eq } from "drizzle-orm";
import { DrizzleD1Database } from "drizzle-orm/d1";
import webhookHandler from "./hono/webhook";
import feedPostsHandler from "./hono/feed/posts";
import syncPostsHandler from "./hono/manage/connection/syncPosts";
import { propelAuthMiddleware } from "./hono/middleware/root";
import { apiKeyAuthMiddleware } from "./hono/middleware/apiKey";
import { userAuthRequired } from "./hono/middleware/user";
import { webhookVerificationMiddleware } from "./hono/middleware/wehook/instagram";
import { getAllProjects } from "./hono/manage/project/getAll";
import getProjectsCount from "./hono/manage/count/projects";
import {
  createProject,
  createProjectValidator,
} from "./hono/manage/project/create";
import { getOneProject } from "./hono/manage/project/get";
import getFeedsCount from "./hono/manage/count/feeds";
import { getAllConnections } from "./hono/manage/project/connections/getAll";
import {
  createConnection,
  createConnectionValidator,
} from "./hono/manage/project/connections/create";
import { getOneProjectConnection } from "./hono/manage/project/connections/get";
import { getAllGeneratedLinks } from "./hono/manage/project/connections/links/getAll";
import {
  createLink,
  createLinkValidator,
} from "./hono/manage/project/connections/links/create";
import { deleteLink } from "./hono/manage/project/connections/links/delete";
import { getAllFeeds } from "./hono/manage/project/feeds/getAll";
import {
  createFeed,
  createFeedValidator,
} from "./hono/manage/project/feeds/create";
import { getOneFeed } from "./hono/manage/project/feeds/get";
import {
  createApiKey,
  createApiKeyValidator,
} from "./hono/manage/project/feeds/apiKey/create";
import { deleteApiKey } from "./hono/manage/project/feeds/apiKey/delete";
import {
  updateFeed,
  updateFeedValidator,
} from "./hono/manage/project/feeds/update";
import { deleteFeed } from "./hono/manage/project/feeds/delete";
import { deleteConnection } from "./hono/manage/project/connections/delete";
import {
  updateConnection,
  updateConnectionValidator,
} from "./hono/manage/project/connections/update";
import {
  updateProject,
  updateProjectValidator,
} from "./hono/manage/project/update";
import { deleteProject } from "./hono/manage/project/delete";
import { countConnectionsOfAccount } from "./hono/manage/count/connections";
import { getProjectConnectionsCount } from "./hono/manage/count/projectConnections";

// --- Typ-Definition für Hono's Umgebung und Kontext ---
// Definiert die Umgebungsvariablen und die Variablen, die wir im Kontext speichern
const app = new Hono<AppContext>()
  // MIDDLEWARE
  .use("*", timing())
  .use("*", secureHeaders()) // Fügt Security Header hinzu
  .use("*", propelAuthMiddleware)
  .use("/feed/:feedId/*", apiKeyAuthMiddleware) // API Key Auth Middleware for /feeds/ endpoints
  .use("/manage/*", userAuthRequired) // User Session Auth Middleware via JWT (for protected endpoints)
  .use("/webhook", webhookVerificationMiddleware) // Webhook Verification Middleware

  // ROUTING
  .post("/webhook", webhookHandler)
  .get("/feed/:feedId/posts", feedPostsHandler)
  .get("/manage/projects", getAllProjects)
  .post("/manage/projects", createProjectValidator, createProject)
  .get("/manage/projects/:projectId", getOneProject)
  .post("/manage/projects/:projectId", updateProjectValidator, updateProject)
  .delete("/manage/projects/:projectId", deleteProject)
  .get("/manage/projects/:projectId/connections", getAllConnections)
  .post(
    "/manage/projects/:projectId/connections",
    createConnectionValidator,
    createConnection
  )
  .get(
    "/manage/projects/:projectId/connections/:connectionId",
    getOneProjectConnection
  )
  .delete(
    "/manage/projects/:projectId/connections/:connectionId",
    deleteConnection
  )
  .post(
    "/manage/projects/:projectId/connections/:connectionId",
    updateConnectionValidator,
    updateConnection
  )
  .get(
    "/manage/projects/:projectId/connections/:connectionId/links",
    getAllGeneratedLinks
  )
  .post(
    "/manage/projects/:projectId/connections/:connectionId/links",
    createLinkValidator,
    createLink
  )
  .delete(
    "/manage/projects/:projectId/connections/:connectionId/links/:linkId",
    deleteLink
  )
  .post("/manage/projects/:projectId/feeds", createFeedValidator, createFeed)
  .get("/manage/projects/:projectId/feeds", getAllFeeds)
  .get("/manage/projects/:projectId/feeds/:feedId", getOneFeed)
  .post(
    "/manage/projects/:projectId/feeds/:feedId",
    updateFeedValidator,
    updateFeed
  )
  .delete("/manage/projects/:projectId/feeds/:feedId", deleteFeed)
  .post(
    "/manage/projects/:projectId/feeds/:feedId/apiKeys",
    createApiKeyValidator,
    createApiKey
  )
  .delete(
    "/manage/projects/:projectId/feeds/:feedId/apiKeys/:apiKey",
    deleteApiKey
  )
  .get("/manage/count/projects", getProjectsCount)
  .get("/manage/count/connections", countConnectionsOfAccount)
  .get(
    "/manage/count/projects/:projectId/connections",
    getProjectConnectionsCount
  )
  .get("/manage/count/projects/:projectId/feeds", getFeedsCount)
  .post(
    "/manage/connection/:platformConnectionId/sync-posts",
    syncPostsHandler
  );

// --- Queue Consumer für Paginierung ---
async function processPaginationQueue(
  batch: MessageBatch<PaginationQueueMessage>,
  env: Bindings,
  ctx: ExecutionContext
): Promise<void> {
  console.log(`Processing ${batch.messages.length} pagination messages...`);
  const db = getDbClient(env.DB);
  const promises = batch.messages.map(async (message) => {
    const { platformConnectionId, mediaId, nextPageUrl } = message.body;
    console.log(
      `Processing next page for pcId=${platformConnectionId} mediaId=${mediaId}`
    );
    try {
      const connection = await getConnectionDetails(db, platformConnectionId);
      if (
        !connection ||
        !connection.isActive ||
        !connection.accessTokenEncrypted
      ) {
        console.warn(
          `Queue Pag: Conn ${platformConnectionId} inactive/no token.`
        );
        return;
      }
      const accessToken = await decryptToken(
        connection.accessTokenEncrypted,
        env
      );
      if (!accessToken) {
        await updateConnectionStatus(db, platformConnectionId, {
          needsReconnect: true,
        });
        logErrorToAnalytics(
          env,
          "QUEUE_PAGINATION_DECRYPT_FAIL",
          `Failed decrypt token`,
          { platformConnectionId, mediaId }
        );
        return;
      }

      // API Call via Service
      const pageResult = await fetchNextCommentPage(nextPageUrl, env);

      if (!pageResult) {
        const currentStatus = await getConnectionDetails(
          db,
          platformConnectionId
        );
        if (!currentStatus?.isActive || currentStatus?.needsReconnect) return; // Kein Retry bei Auth Fehler
        throw new Error(`Failed to fetch next comment page (API Client Error)`);
      }
      const { comments: commentsToWrite, nextPageUrl: subsequentNextPageUrl } =
        pageResult;

      // Skip comment processing - comments are not stored
      console.log(
        `Queue Pag: Skipping ${commentsToWrite.length} comments for media ${mediaId} (comments not stored)`
      );
      // Enqueue next page
      if (subsequentNextPageUrl) {
        const nextMessage: PaginationQueueMessage = {
          platformConnectionId,
          mediaId,
          nextPageUrl: subsequentNextPageUrl,
        };
        await env.PAGINATION_QUEUE.send(nextMessage);
      }
    } catch (error: any) {
      console.error(
        `Queue Pag: Failed processing message for pcId=${platformConnectionId} mediaId=${mediaId}, attempt ${message.attempts}:`,
        error
      );
      const maxRetries = parseInt(env.QUEUE_MAX_RETRIES || "5", 10);
      if (message.attempts >= maxRetries) {
        logErrorToAnalytics(
          env,
          "QUEUE_FINAL_FAILURE",
          `Pagination message failed all retries`,
          {
            platformConnectionId,
            mediaId,
            nextPageUrl,
            attempts: message.attempts,
            error: String(error),
          }
        );
      } else {
        message.retry({ delaySeconds: 30 * (message.attempts + 1) });
      } // Retry mit Backoff
    }
  });
  await Promise.allSettled(promises);
}

// --- Queue Consumer für Post Sync ---
async function processSyncQueue(
  batch: MessageBatch<SyncQueueMessage>,
  env: Bindings,
  ctx: ExecutionContext
): Promise<void> {
  console.log(`Processing ${batch.messages.length} sync messages...`);
  const db = getDbClient(env.DB);
  const promises = batch.messages.map(async (message) => {
    const { platformConnectionId, triggeredByUserId } = message.body;
    console.log(
      `Sync Queue: Starting sync for connection ${platformConnectionId}`
    );
    try {
      const connection = await getConnectionDetails(db, platformConnectionId);
      if (
        !connection ||
        !connection.isActive ||
        !connection.accessTokenEncrypted
      ) {
        /* Log & return */ return;
      }
      const accessToken = await decryptToken(
        connection.accessTokenEncrypted,
        env
      );
      if (!accessToken) {
        await updateConnectionStatus(db, platformConnectionId, {
          needsReconnect: true,
        });
        return;
      }

      // API Call via Platform Adapter
      const platformAdapter = getPlatformAdapter(connection.platform);
      const syncResult = await platformAdapter.syncPosts(
        connection,
        accessToken,
        env,
        100
      );

      const posts = syncResult.posts || [];

      // Update platform-specific metrics (e.g., YouTube subscriber count)
      if (syncResult.channelMetrics && connection.platform === "youtube") {
        try {
          await db
            .update(schema.platformConnections)
            .set({
              subscriberCount: syncResult.channelMetrics.subscriberCount,
              platformMetrics: JSON.stringify(syncResult.channelMetrics),
              lastPolledAt: new Date(),
            })
            .where(eq(schema.platformConnections.id, platformConnectionId));
        } catch (updateError) {
          console.error(
            `Failed to update platform metrics for ${platformConnectionId}:`,
            updateError
          );
        }
      }

      // DB Write via Service
      if (posts && posts.length > 0) {
        console.log(
          `Sync Queue: Upserting ${posts.length} posts for ${platformConnectionId}`
        );
        for (const post of posts) {
          const postData = platformAdapter.mapPostData(post, connection);
          try {
            await upsertPost(db, postData);
          } catch (d1Error) {
            logErrorToAnalytics(
              env,
              "SYNC_DB_POST_ERROR",
              `Error upserting post ${post.id} during sync`,
              { platformConnectionId, mediaId: post.id, error: String(d1Error) }
            );
          }
        }
      }
      console.log(
        `Sync Queue: Finished sync successfully for connection ${platformConnectionId}`
      );
    } catch (error: any) {
      console.error(
        `Sync Queue: CRITICAL error processing message for pcId=${platformConnectionId}:`,
        error
      );
      const maxRetries = parseInt(env.QUEUE_MAX_RETRIES || "3", 10); // Ggf. anderes Retry Limit für Sync
      if (message.attempts >= maxRetries) {
        logErrorToAnalytics(
          env,
          "SYNC_FINAL_FAILURE",
          `Sync message failed all retries`,
          {
            platformConnectionId,
            attempts: message.attempts,
            error: String(error),
          }
        );
      } else {
        message.retry({ delaySeconds: 60 * (message.attempts + 1) });
      } // Längeres Backoff für Sync?
    }
  });
  await Promise.allSettled(promises);
}

// --- Scheduled Handler für Token Check ---
async function handleTokenValidityCheck(env: Bindings, ctx: ExecutionContext) {
  console.log(`Scheduled token validity check running...`);
  const db = getDbClient(env.DB);
  const now = Date.now();
  const checkThreshold = now - 23 * 60 * 60 * 1000; // 23 hours ago
  try {
    const connectionsToCheck = await getConnectionsForTokenCheck(
      db,
      checkThreshold
    ); // DB Service
    console.log(
      `Found ${connectionsToCheck.length} connections for validity check.`
    );
    for (const conn of connectionsToCheck) {
      let accessToken: string | null = null;
      if (conn.accessTokenEncrypted)
        accessToken = await decryptToken(conn.accessTokenEncrypted, env); // Token Util
      if (!accessToken) {
        await updateConnectionStatus(db, conn.id, { needsReconnect: true });
        logErrorToAnalytics(
          env,
          "TOKEN_CHECK_DECRYPT_FAIL",
          `Failed decrypt token`,
          { platformConnectionId: conn.id }
        );
        continue;
      }

      let isValid = false;
      try {
        try {
          const platformAdapter = getPlatformAdapter(conn.platform);
          isValid = await platformAdapter.checkToken(accessToken, env);

          if (!isValid) {
            await updateConnectionStatus(db, conn.id, { needsReconnect: true });
            logErrorToAnalytics(
              env,
              "TOKEN_CHECK_FAIL",
              `${conn.platform} token invalid`,
              { platformConnectionId: conn.id, platform: conn.platform }
            );
          }
        } catch (adapterError) {
          console.error(
            `Token check failed for platform ${conn.platform}:`,
            adapterError
          );
          logErrorToAnalytics(
            env,
            "TOKEN_CHECK_ADAPTER_ERROR",
            `Platform adapter error for ${conn.platform}`,
            { platformConnectionId: conn.id, error: String(adapterError) }
          );
        }
        if (isValid) {
          await db
            .update(schema.platformConnections)
            .set({ lastCheckedAt: new Date() })
            .where(eq(schema.platformConnections.id, conn.id));
        }
      } catch (fetchError) {
        logErrorToAnalytics(
          env,
          "TOKEN_CHECK_NETWORK_ERROR",
          `Network error during check`,
          { platformConnectionId: conn.id, error: String(fetchError) }
        );
      }
    }
  } catch (error) {
    console.error("CRITICAL Error during scheduled token check:", error);
    logErrorToAnalytics(
      env,
      "SCHEDULED_HANDLER_ERROR",
      `Token check handler failed`,
      { error: String(error) }
    );
  }
}
// Helper für Token Check Fehlerbehandlung
async function handleTokenCheckApiError(
  db: DrizzleD1Database<typeof schema>,
  env: Bindings,
  conn: Pick<PlatformConnection, "id">,
  response: Response
) {
  console.warn(
    `Token Check: API Error Status ${response.status} for conn ${conn.id}`
  );
  if ([400, 401, 403].includes(response.status)) {
    await updateConnectionStatus(db, conn.id, { needsReconnect: true }); // DB Service
    logErrorToAnalytics(
      env,
      "TOKEN_CHECK_AUTH_FAIL",
      `Token invalid or permissions revoked`,
      { platformConnectionId: conn.id, status: response.status }
    );
  } else {
    logErrorToAnalytics(
      env,
      "TOKEN_CHECK_API_ERROR",
      `API error during check, status ${response.status}`,
      { platformConnectionId: conn.id, status: response.status }
    );
  }
}

// --- Scheduled Handler für Basic Display Polling ---
async function handleBasicDisplayPolling(
  env: Bindings,
  ctx: ExecutionContext
): Promise<void> {
  console.log(`Basic Display Polling running...`);
  const db = getDbClient(env.DB);
  try {
    const connectionsToPoll = await getActiveBasicDisplayConnections(db); // DB Service
    console.log(
      `Polling ${connectionsToPoll.length} active Basic Display connections.`
    );
    for (const conn of connectionsToPoll) {
      let accessToken: string | null = null;
      try {
        if (!conn.accessTokenEncrypted) continue;
        accessToken = await decryptToken(conn.accessTokenEncrypted, env); // Token Util
        if (!accessToken) {
          await updateConnectionStatus(db, conn.id, { needsReconnect: true });
          continue;
        } // DB Service

        // API Call via Platform Adapter
        const platformAdapter = getPlatformAdapter(conn.platform);
        const syncResult = await platformAdapter.syncPosts(
          conn,
          accessToken,
          env,
          50
        );

        if (syncResult.error) {
          if (
            syncResult.error instanceof GraphApiError &&
            syncResult.error.isAuthError
          ) {
            await updateConnectionStatus(db, conn.id, { needsReconnect: true });
          } else {
            throw syncResult.error;
          }
          continue;
        }

        // DB Vergleich & Write via Service
        const posts = syncResult.posts || [];
        if (posts.length > 0) {
          // Hole existierende IDs
          const existingMediaIds = new Set(
            (
              await db
                .select({ mediaId: schema.posts.mediaId })
                .from(schema.posts)
                .where(eq(schema.posts.platformConnectionId, conn.id))
                .orderBy(desc(schema.posts.timestamp))
                .limit(200) // Schaue weiter zurück beim Poll
                .all()
            ).map((p) => p.mediaId)
          );

          let newPostsFound = 0;
          const platformAdapter = getPlatformAdapter(conn.platform);

          for (const post of posts) {
            if (!existingMediaIds.has(post.id)) {
              newPostsFound++;
              const postData = platformAdapter.mapPostData(post, conn);
              try {
                await upsertPost(db, postData);
              } catch (d1Error) {
                // DB Service
                logErrorToAnalytics(
                  env,
                  "POLLING_DB_POST_ERROR",
                  `Error upserting polled post ${post.id}`,
                  {
                    platformConnectionId: conn.id,
                    mediaId: post.id,
                    error: String(d1Error),
                  }
                );
              }
            }
          }
          if (newPostsFound > 0)
            console.log(
              `Polling: Found ${newPostsFound} new posts for ${conn.id}.`
            );
        }
        // Optional: Update lastPolledAt
        // await db.update(schema.platformConnections).set({ lastPolledAt: new Date() }).where(eq(schema.platformConnections.id, conn.id));
      } catch (connError: any) {
        console.error(
          `Polling: Unhandled error processing connection ${conn.id}:`,
          connError
        );
        logErrorToAnalytics(
          env,
          "POLLING_CONN_ERROR",
          `Unhandled error polling connection`,
          { platformConnectionId: conn.id, error: String(connError) }
        );
      }
    }
  } catch (error) {
    console.error("CRITICAL Error during Basic Display Polling:", error);
    logErrorToAnalytics(
      env,
      "POLLING_HANDLER_ERROR",
      `Polling handler failed`,
      { error: String(error) }
    );
  }
}

/**
 * Haupt-Handler für Scheduled Events (Cron Triggers).
 * Leitet die Anfrage basierend auf dem Cron-String an die zuständige Funktion weiter.
 */
async function handleScheduled(
  event: ScheduledEvent,
  env: Bindings,
  ctx: ExecutionContext
) {
  console.log(`Scheduled event triggered by cron: ${event.cron}`);
  // Nutze waitUntil, um sicherzustellen, dass die Verarbeitung Zeit hat, auch wenn der Trigger kurzlebig ist
  if (event.cron === "*/5 * * * *") {
    // Alle 5 Minuten
    ctx.waitUntil(handleBasicDisplayPolling(env, ctx));
  } else if (event.cron === "5 3 * * *") {
    // Täglich um 03:05 UTC
    ctx.waitUntil(handleTokenValidityCheck(env, ctx));
  } else {
    console.warn(`Scheduled event triggered by unexpected cron: ${event.cron}`);
    // Logge diesen unerwarteten Trigger auch
    logErrorToAnalytics(
      env,
      "UNKNOWN_CRON",
      `Unexpected cron trigger received`,
      { cron: event.cron }
    );
  }
}

// --- Exports ---
export { DebouncerDO }; // Exportiere DO Klasse für wrangler.toml
export default {
  fetch: app.fetch, // Hono Request Handler
  queue: async (
    batch: MessageBatch<any>,
    env: Bindings,
    ctx: ExecutionContext
  ) => {
    // Haupt Queue Router
    try {
      // Leite an den richtigen Consumer weiter basierend auf Queue-Namen
      if (batch.queue === "pagination-queue")
        await processPaginationQueue(
          batch as MessageBatch<PaginationQueueMessage>,
          env,
          ctx
        );
      else if (batch.queue === "sync-queue")
        await processSyncQueue(
          batch as MessageBatch<SyncQueueMessage>,
          env,
          ctx
        );
      // Optional: Consumer für DLQs hier hinzufügen, falls gewünscht
      // else if (batch.queue === 'pagination-dlq') await processPaginationDLQ(batch, env, ctx);
      // else if (batch.queue === 'sync-dlq') await processSyncDLQ(batch, env, ctx);
      else {
        console.error(`Msg from unknown queue: ${batch.queue}`);
        logErrorToAnalytics(
          env,
          "UNKNOWN_QUEUE_MESSAGE",
          `Msg from queue ${batch.queue}`,
          { count: batch.messages.length }
        );
        batch.retryAll();
      }
    } catch (e) {
      console.error(`Unhandled queue processor error for ${batch.queue}:`, e);
      logErrorToAnalytics(
        env,
        "QUEUE_PROCESSOR_FATAL",
        `Unhandled error in queue ${batch.queue}`,
        { error: String(e) }
      );
      batch.retryAll();
    }
  },
  scheduled: handleScheduled, // Scheduled Event Handler
};

export type AppType = typeof app;
