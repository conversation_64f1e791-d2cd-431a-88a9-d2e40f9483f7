// src/hono/webhook/youtube.ts
import { Context } from "hono";
import { AppContext } from "../../types";
import { HTTPException } from "hono/http-exception";
import { getDbClient } from "../../database-service";
import * as schema from "@repo/drizzle-schema/d1";
import { logErrorToAnalytics } from "../../analytics-utils";
import { eq } from "drizzle-orm";
import { z } from "zod";

// YouTube PubSubHubbub XML feed schema
const YouTubeFeedSchema = z.object({
  feed: z.object({
    entry: z
      .array(
        z.object({
          id: z.string(),
          "yt:videoId": z.string(),
          "yt:channelId": z.string(),
          title: z.string(),
          link: z.object({
            "@_href": z.string(),
          }),
          author: z.object({
            name: z.string(),
            uri: z.string(),
          }),
          published: z.string(),
          updated: z.string(),
        })
      )
      .optional(),
  }),
});

export const youtubeWebhookHandler = async (c: Context<AppContext>) => {
  // Handle verification challenge
  if (c.req.method === "GET") {
    return handleVerificationChallenge(c);
  }

  // Handle notification
  if (c.req.method === "POST") {
    return handleNotification(c);
  }

  throw new HTTPException(405, { message: "Method not allowed" });
};

/**
 * Handle YouTube PubSubHubbub verification challenge
 */
async function handleVerificationChallenge(c: Context<AppContext>) {
  const mode = c.req.query("hub.mode");
  const topic = c.req.query("hub.topic");
  const challenge = c.req.query("hub.challenge");
  const verifyToken = c.req.query("hub.verify_token");

  console.log("YouTube webhook verification:", {
    mode,
    topic,
    challenge,
    verifyToken,
  });

  // Verify the request
  const expectedToken = c.env.YOUTUBE_VERIFY_TOKEN || "youtube_verify_token";

  if (mode === "subscribe" && verifyToken === expectedToken && challenge) {
    console.log("YouTube webhook verification successful for topic:", topic);

    // Extract channel ID from topic URL
    const channelId = extractChannelIdFromTopic(topic || "");
    if (channelId) {
      // Log successful subscription
      logErrorToAnalytics(
        c.env,
        "YOUTUBE_SUBSCRIPTION_VERIFIED",
        "YouTube subscription verified",
        { channelId, topic }
      );
    }

    return c.text(challenge);
  }

  if (mode === "unsubscribe" && verifyToken === expectedToken && challenge) {
    console.log("YouTube webhook unsubscribe verification for topic:", topic);
    return c.text(challenge);
  }

  console.error("YouTube webhook verification failed:", {
    mode,
    verifyToken,
    expectedToken,
  });
  throw new HTTPException(403, { message: "Verification failed" });
}

/**
 * Handle YouTube PubSubHubbub notification
 */
async function handleNotification(c: Context<AppContext>) {
  try {
    const body = await c.req.text();
    console.log("YouTube webhook notification received:", body);

    // Parse XML feed (simplified - in production use a proper XML parser)
    const notification = parseYouTubeFeed(body);

    if (!notification) {
      console.log("No video data in YouTube notification");
      return c.text("OK");
    }

    const { channelId, videoId, title, publishedAt } = notification;

    // Find the connection for this channel
    const db = getDbClient(c.env.DB);
    const connection = await db
      .select()
      .from(schema.platformConnections)
      .where(eq(schema.platformConnections.platformAccountId, channelId))
      .get();

    if (!connection) {
      console.log(`No connection found for YouTube channel: ${channelId}`);
      return c.text("OK");
    }

    if (!connection.isActive) {
      console.log(`Connection inactive for YouTube channel: ${channelId}`);
      return c.text("OK");
    }

    // Trigger sync for this connection
    try {
      await c.env.SYNC_QUEUE.send({
        platformConnectionId: connection.id,
        triggeredByUserId: "youtube_webhook",
      });

      console.log(
        `Triggered sync for YouTube connection: ${connection.id} due to new video: ${videoId}`
      );

      logErrorToAnalytics(
        c.env,
        "YOUTUBE_WEBHOOK_SYNC_TRIGGERED",
        "YouTube webhook triggered sync",
        {
          connectionId: connection.id,
          channelId,
          videoId,
          title,
          publishedAt,
        }
      );
    } catch (queueError) {
      console.error("Failed to queue YouTube sync:", queueError);
      logErrorToAnalytics(
        c.env,
        "YOUTUBE_WEBHOOK_QUEUE_ERROR",
        "Failed to queue YouTube sync",
        {
          connectionId: connection.id,
          channelId,
          videoId,
          error: String(queueError),
        }
      );
    }

    return c.text("OK");
  } catch (error: any) {
    console.error("YouTube webhook notification error:", error);
    logErrorToAnalytics(
      c.env,
      "YOUTUBE_WEBHOOK_ERROR",
      "YouTube webhook processing failed",
      { error: String(error) }
    );

    // Return OK to prevent retries for malformed requests
    return c.text("OK");
  }
}

/**
 * Extract channel ID from YouTube topic URL
 */
function extractChannelIdFromTopic(topic: string | null): string | null {
  if (!topic) return null;

  const match = topic.match(/channel_id=([^&]+)/);
  return match ? match[1] : null;
}

/**
 * Parse YouTube XML feed (simplified parser)
 * In production, use a proper XML parser like fast-xml-parser
 */
function parseYouTubeFeed(xmlBody: string): {
  channelId: string;
  videoId: string;
  title: string;
  publishedAt: string;
} | null {
  try {
    // Extract video ID
    const videoIdMatch = xmlBody.match(/<yt:videoId>([^<]+)<\/yt:videoId>/);
    if (!videoIdMatch) return null;

    // Extract channel ID
    const channelIdMatch = xmlBody.match(
      /<yt:channelId>([^<]+)<\/yt:channelId>/
    );
    if (!channelIdMatch) return null;

    // Extract title
    const titleMatch = xmlBody.match(/<title>([^<]+)<\/title>/);
    const title = titleMatch ? titleMatch[1] : "Unknown Title";

    // Extract published date
    const publishedMatch = xmlBody.match(/<published>([^<]+)<\/published>/);
    const publishedAt = publishedMatch
      ? publishedMatch[1]
      : new Date().toISOString();

    return {
      channelId: channelIdMatch[1],
      videoId: videoIdMatch[1],
      title,
      publishedAt,
    };
  } catch (error) {
    console.error("Failed to parse YouTube feed:", error);
    return null;
  }
}
