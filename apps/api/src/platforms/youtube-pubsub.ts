// src/platforms/youtube-pubsub.ts
import { Bindings } from "../types";
import { logErrorToAnalytics } from "../analytics-utils";
import * as schema from "@repo/drizzle-schema/d1";
import { and, eq } from "drizzle-orm";

export interface YouTubeSubscription {
  channelId: string;
  topicUrl: string;
  callbackUrl: string;
  expiresAt?: Date;
}

/**
 * Subscribe to YouTube PubSubHubbub notifications for a channel
 */
export async function subscribeToYouTubeChannel(
  channelId: string,
  env: Bindings
): Promise<void> {
  const hubUrl = "https://pubsubhubbub.appspot.com/subscribe";
  const topicUrl = `https://www.youtube.com/xml/feeds/videos.xml?channel_id=${channelId}`;
  const callbackUrl = `${env.NEXT_PUBLIC_API_URL}/webhooks/youtube`;

  const formData = new URLSearchParams({
    "hub.callback": callbackUrl,
    "hub.topic": topicUrl,
    "hub.verify": "async",
    "hub.mode": "subscribe",
    "hub.verify_token": env.YOUTUBE_VERIFY_TOKEN || "youtube_verify_token",
    "hub.lease_seconds": "864000", // 10 days
  });

  try {
    const response = await fetch(hubUrl, {
      method: "POST",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
      body: formData,
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(
        `YouTube subscription failed: ${response.status} ${errorText}`
      );
    }

    console.log(
      `Successfully initiated subscription for YouTube channel: ${channelId}`
    );

    logErrorToAnalytics(
      env,
      "YOUTUBE_SUBSCRIPTION_INITIATED",
      "YouTube subscription initiated",
      { channelId, topicUrl, callbackUrl }
    );
  } catch (error: any) {
    console.error(
      `Failed to subscribe to YouTube channel ${channelId}:`,
      error
    );
    logErrorToAnalytics(
      env,
      "YOUTUBE_SUBSCRIPTION_FAILED",
      "YouTube subscription failed",
      { channelId, error: String(error) }
    );
    throw error;
  }
}

/**
 * Unsubscribe from YouTube PubSubHubbub notifications for a channel
 */
export async function unsubscribeFromYouTubeChannel(
  channelId: string,
  env: Bindings
): Promise<void> {
  const hubUrl = "https://pubsubhubbub.appspot.com/subscribe";
  const topicUrl = `https://www.youtube.com/xml/feeds/videos.xml?channel_id=${channelId}`;
  const callbackUrl = `${env.NEXT_PUBLIC_API_URL}/webhooks/youtube`;

  const formData = new URLSearchParams({
    "hub.callback": callbackUrl,
    "hub.topic": topicUrl,
    "hub.verify": "async",
    "hub.mode": "unsubscribe",
    "hub.verify_token": env.YOUTUBE_VERIFY_TOKEN || "youtube_verify_token",
  });

  try {
    const response = await fetch(hubUrl, {
      method: "POST",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
      body: formData,
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(
        `YouTube unsubscription failed: ${response.status} ${errorText}`
      );
    }

    console.log(
      `Successfully initiated unsubscription for YouTube channel: ${channelId}`
    );

    logErrorToAnalytics(
      env,
      "YOUTUBE_UNSUBSCRIPTION_INITIATED",
      "YouTube unsubscription initiated",
      { channelId, topicUrl, callbackUrl }
    );
  } catch (error: any) {
    console.error(
      `Failed to unsubscribe from YouTube channel ${channelId}:`,
      error
    );
    logErrorToAnalytics(
      env,
      "YOUTUBE_UNSUBSCRIPTION_FAILED",
      "YouTube unsubscription failed",
      { channelId, error: String(error) }
    );
    throw error;
  }
}

/**
 * Renew YouTube PubSubHubbub subscriptions
 * Should be called periodically (every 6-8 hours) to maintain subscriptions
 */
export async function renewYouTubeSubscriptions(
  channelIds: string[],
  env: Bindings
): Promise<{ success: string[]; failed: string[] }> {
  const results = { success: [] as string[], failed: [] as string[] };

  for (const channelId of channelIds) {
    try {
      await subscribeToYouTubeChannel(channelId, env);
      results.success.push(channelId);
    } catch (error) {
      console.error(
        `Failed to renew subscription for channel ${channelId}:`,
        error
      );
      results.failed.push(channelId);
    }
  }

  logErrorToAnalytics(
    env,
    "YOUTUBE_SUBSCRIPTION_RENEWAL",
    "YouTube subscription renewal completed",
    {
      totalChannels: channelIds.length,
      successful: results.success.length,
      failed: results.failed.length,
      failedChannels: results.failed,
    }
  );

  return results;
}

/**
 * Get all active YouTube channel IDs that need subscription renewal
 */
export async function getActiveYouTubeChannels(db: any): Promise<string[]> {
  try {
    const connections = await db
      .select({
        platformAccountId: schema.platformConnections.platformAccountId,
      })
      .from(schema.platformConnections)
      .where(
        and(
          eq(schema.platformConnections.platform, "youtube"),
          eq(schema.platformConnections.isActive, true),
          eq(schema.platformConnections.isConnected, true)
        )
      )
      .all();

    return connections
      .map((conn: any) => conn.platformAccountId)
      .filter((id: string) => id); // Filter out null/undefined IDs
  } catch (error) {
    console.error("Failed to get active YouTube channels:", error);
    return [];
  }
}
