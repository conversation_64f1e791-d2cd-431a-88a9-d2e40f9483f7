// src/platforms/meta.ts
import { z } from "zod";
import {
  ApiFetchResult,
  Bindings,
  GraphApiMedia,
  GraphApiMediaSchema,
  GraphApiCommentsResponseSchema,
  BasicDisplayApiResponseSchema,
  BatchApiResponseItemSchema,
  GraphApiComment,
  BasicDisplayApiMediaItem,
  GraphApiMeSchema,
  GraphApiCommentsResponse,
  GraphApiPagingSchema,
} from "../types";
import { logErrorToAnalytics } from "../analytics-utils";
import { fetchWithRetry } from "../graph-api-shared";
import * as constants from "../constants";

// --- Graph API Functions for Facebook/Instagram ---

export async function fetchMediaDataAndFirstComments(
  mediaId: string,
  accessToken: string,
  env: Bindings
): Promise<ApiFetchResult | null> {
  const contextInfo = `Media ${mediaId}`;
  let baseData: GraphApiMedia | undefined;
  let fetchedComments: GraphApiComment[] = [];
  let nextPageUrl: string | null = null;
  try {
    // 1. Fetch Base Media Data
    const baseFields =
      "id,caption,like_count,comments_count,media_url,timestamp,username,media_type,permalink,thumbnail_url,children{id,media_url,media_type}";
    const baseUrl = `https://graph.facebook.com/${constants.META_API_VERSION}/${mediaId}?fields=${baseFields}&access_token=${accessToken}`;
    const baseResponse = await fetchWithRetry(
      baseUrl,
      {},
      `${contextInfo} Base`,
      env
    );

    const baseJson = await baseResponse.json();
    const baseValidation = GraphApiMediaSchema.safeParse(baseJson);
    if (!baseValidation.success) {
      logErrorToAnalytics(
        env,
        "API_VALIDATION_ERROR",
        "Invalid Graph API media response",
        { errors: baseValidation.error.flatten(), received: baseJson }
      );
      return null;
    }
    baseData = baseValidation.data;

    // 2. Fetch First Comments Page
    const commentsFields = "id,text,timestamp,from{id,username}";
    const commentsUrl = `https://graph.facebook.com/${constants.META_API_VERSION}/${mediaId}/comments?fields=${commentsFields}&limit=25&access_token=${accessToken}`;
    try {
      // Separate try-catch for comments, base data might still be useful
      const commentsResponse = await fetchWithRetry(
        commentsUrl,
        {},
        `${contextInfo} Comments P1`,
        env
      );
      const commentsJson = await commentsResponse.json();
      const commentsValidation =
        GraphApiCommentsResponseSchema.safeParse(commentsJson);
      if (commentsValidation.success) {
        fetchedComments = commentsValidation.data.data || [];
        nextPageUrl = commentsValidation.data.paging?.next || null;
      } else {
        logErrorToAnalytics(
          env,
          "API_VALIDATION_ERROR",
          "Invalid Graph API comments response",
          { errors: commentsValidation.error.flatten(), received: commentsJson }
        );
      }
    } catch (commentsError) {
      console.warn(
        `${contextInfo}: Comments fetch failed, continuing with base data:`,
        commentsError
      );
    }

    return { baseData, fetchedComments, nextPageUrl };
  } catch (error: any) {
    console.error(`Graph API: Failed to fetch ${contextInfo}:`, error);
    logErrorToAnalytics(
      env,
      "GRAPH_MEDIA_FETCH_ERROR",
      "Failed fetching graph media",
      { mediaId, error: String(error) }
    );
    return null;
  }
}

export async function fetchLatestGraphMedia(
  accessToken: string,
  platformAccountId: string, // IG User ID oder FB Page ID
  env: Bindings,
  limit: number = 100
): Promise<{ posts: GraphApiMedia[]; error?: any } | null> {
  const contextInfo = `Latest Graph Media for ${platformAccountId}`;
  const collectedPosts: GraphApiMedia[] = [];
  let fetchLimitPerPage = Math.min(limit, 50); // Beispiel: Max 50 pro Seite
  const fields =
    "id,caption,like_count,comments_count,media_url,timestamp,username,media_type,permalink,thumbnail_url,children{id,media_url,media_type}";
  let url: string | null =
    `https://graph.facebook.com/${constants.META_API_VERSION}/${platformAccountId}/media?fields=${fields}&limit=${fetchLimitPerPage}&access_token=${accessToken}`;

  try {
    while (url && collectedPosts.length < limit) {
      const remainingLimit = limit - collectedPosts.length;
      const currentLimit = Math.min(fetchLimitPerPage, remainingLimit);
      const urlObj = new URL(url);
      urlObj.searchParams.set("limit", currentLimit.toString());
      url = urlObj.toString();

      console.log(
        `GRAPH_API: Fetching graph media page for ${platformAccountId}...`
      );
      const response = await fetchWithRetry(
        url,
        {},
        `${contextInfo} Page`,
        env
      ); // Nutze Retry
      const pageJson = await response.json();

      // Validiere die Antwortstruktur (erwartet { data: [], paging: {} })
      const pageValidation = z
        .object({
          data: z.array(GraphApiMediaSchema),
          paging: GraphApiPagingSchema.optional(),
        })
        .safeParse(pageJson);

      if (!pageValidation.success) {
        logErrorToAnalytics(
          env,
          "GRAPH_MEDIA_PAGE_VALIDATION_ERROR",
          "Invalid graph media page response",
          { errors: pageValidation.error.flatten(), received: pageJson }
        );
        break;
      }
      const pageData = pageValidation.data;

      if (pageData.data && pageData.data.length > 0) {
        collectedPosts.push(...pageData.data);
      } else {
        break;
      } // Keine Daten mehr
      if (collectedPosts.length >= limit) break; // Limit erreicht

      url = pageData.paging?.next || null; // Nächste Seite
      if (url)
        console.log(
          `GRAPH_API: Fetching next graph media page for ${platformAccountId}...`
        );
    }
    console.log(
      `GRAPH_API: Fetched ${collectedPosts.length} graph posts for ${platformAccountId}.`
    );
    return { posts: collectedPosts.slice(0, limit) };
  } catch (error: any) {
    console.error(
      `GRAPH_API: Failed to fetch latest graph media for ${platformAccountId}:`,
      error
    );
    logErrorToAnalytics(
      env,
      "GRAPH_MEDIA_FETCH_ERROR",
      "Failed fetching graph media",
      { platformAccountId, error: String(error) }
    );
    return {
      posts: [],
      error: error,
    };
  }
}

export async function fetchBasicDisplayMedia(
  accessToken: string,
  env: Bindings,
  limit: number = 25
): Promise<{ posts: BasicDisplayApiMediaItem[]; error?: any } | null> {
  const contextInfo = `Basic Display Media`;
  const fields =
    "id,caption,media_type,media_url,permalink,thumbnail_url,timestamp,username";
  let url: string | null =
    `https://graph.instagram.com/${constants.BASIC_DISPLAY_API_VERSION}/me/media?fields=${fields}&limit=${limit}&access_token=${accessToken}`;
  try {
    const response = await fetch(url); // Einfacher Fetch hier
    if (!response.ok) {
      const errorText = await response.text();
      const isAuth = [400, 401, 403].includes(response.status);
      console.error(
        `GRAPH_API (Basic): Error fetching media: ${response.status}`,
        errorText
      );
      return {
        posts: [],
        error: { status: response.status, isAuth, message: errorText },
      };
    }

    const jsonResp = await response.json();
    const validation = BasicDisplayApiResponseSchema.safeParse(jsonResp);
    if (!validation.success) {
      logErrorToAnalytics(
        env,
        "API_VALIDATION_ERROR",
        "Invalid Basic Display response",
        { errors: validation.error.flatten(), received: jsonResp }
      );
      return { posts: [], error: "Validation failed" };
    }

    return { posts: validation.data.data };
  } catch (error: any) {
    console.error("Basic Display media fetch failed:", error);
    return { posts: [], error };
  }
}

export async function checkBasicDisplayToken(
  accessToken: string,
  env: Bindings
): Promise<boolean> {
  const url = `https://graph.instagram.com/${constants.BASIC_DISPLAY_API_VERSION}/me?fields=id&access_token=${accessToken}`;
  try {
    const response = await fetch(url);
    if (!response.ok) return false;
    const jsonResp = await response.json();
    const validation = GraphApiMeSchema.safeParse(jsonResp);
    if (!validation.success) {
      logErrorToAnalytics(
        env,
        "API_VALIDATION_ERROR",
        "Invalid Basic Display /me response",
        { errors: validation.error.flatten(), received: jsonResp }
      );
      return false;
    }
    return true;
  } catch (e) {
    console.error("Basic Display token check failed:", e);
    return false;
  }
}
