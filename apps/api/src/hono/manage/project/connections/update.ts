import { Context } from "hono";
import { AppContext } from "../../../../types";
import { HTTPException } from "hono/http-exception";
import { getDbClient } from "../../../../database-service";
import * as schema from "@repo/drizzle-schema/d1";
import { logErrorToAnalytics } from "../../../../analytics-utils";
import { fixUserAndOrgTable } from "../../../manage/account/fix";
import { DateTime } from "luxon";
import { zValidator } from "@hono/zod-validator";
import { z } from "zod";
import { and, eq } from "drizzle-orm";

const validator = z.object({
  name: z.string().optional().nullable(),
  isActive: z.boolean().optional().nullable(),
});

export const updateConnectionValidator = zValidator("json", validator);

export const updateConnection = async (
  c: Context<
    AppContext,
    "/manage/projects/:projectId/connection/:connectionId",
    {
      in: {
        json: z.infer<typeof validator>;
      };
      out: {
        json: z.infer<typeof validator>;
      };
    }
  >
) => {
  const organizationId = c.var.organizationId;
  if (!organizationId)
    throw new HTTPException(403, {
      message: "Organization context required",
    });

  const validated = c.req.valid("json");

  const name = validated.name;

  const isActive = validated.isActive;

  const userId = c.var.user?.userId;

  if (!userId)
    throw new HTTPException(400, { message: "You must be logged in" });

  const projectId = c.req.param("projectId");
  const connectionId = c.req.param("connectionId");

  const db = getDbClient(c.env.DB);
  try {
    const valid = await db
      .select({
        id: schema.platformConnections.id,
      })
      .from(schema.platformConnections)
      .innerJoin(
        schema.projects,
        eq(schema.platformConnections.projectId, schema.projects.id)
      )
      .where(
        and(
          eq(schema.feeds.id, connectionId),
          eq(schema.projects.id, projectId),
          eq(schema.projects.organizationId, organizationId)
        )
      )
      .get();

    if (!valid) {
      throw new HTTPException(404, { message: "Project not found" });
    }

    await db
      .update(schema.feeds)
      .set({
        ...(name && { name }),
        ...(isActive !== undefined &&
          isActive !== null && { isActive: isActive }),
        updatedAt: DateTime.now().toJSDate(),
      })
      .where(eq(schema.platformConnections.id, connectionId))
      .get();

    return c.json({
      success: true,
    });
  } catch (e) {
    console.error(
      `Failed to update connection ${connectionId} on project ${projectId} for org ${organizationId}:`,
      e
    );
    logErrorToAnalytics(
      c.env,
      "CONNECTION_UPDATE_ERROR",
      `Failed to update connection`,
      {
        connectionId,
        projectId,
        organizationId,
        error: String(e),
      }
    );
    await fixUserAndOrgTable({ user: c.var.user, d1: c.env.DB });

    throw new HTTPException(500, {
      message: "Failed to update connection",
    });
  }
};
