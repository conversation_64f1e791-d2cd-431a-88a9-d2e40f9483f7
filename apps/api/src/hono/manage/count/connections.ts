import { Context } from "hono";
import { AppContext } from "../../../types";
import { BlankInput } from "hono/types";
import { HTTPException } from "hono/http-exception";
import { getDbClient } from "../../../database-service";
import { logErrorToAnalytics } from "../../../analytics-utils";
import * as schema from "@repo/drizzle-schema/d1";
import { and, eq, sql } from "drizzle-orm";

export const countConnectionsOfAccount = async (
  c: Context<AppContext, "/manage/count/connections", BlankInput>
) => {
  const organizationId = c.var.organizationId;
  if (!organizationId)
    throw new HTTPException(403, {
      message: "Organization context required",
    });

  const db = getDbClient(c.env.DB);
  try {
    const connections = await db
      .select({
        count: sql<number>`count(*)`,
        countActive: sql<number>`count(case when platform_connections.is_active = 1 then 1 end)`,
        countConnected: sql<number>`count(case when platform_connections.is_connected = 1 then 1 end)`,
      })
      .from(schema.platformConnections)
      .innerJoin(
        schema.projects,
        eq(schema.platformConnections.projectId, schema.projects.id)
      )
      .where(and(eq(schema.projects.organizationId, organizationId)))
      .get();

    return c.json(connections);
  } catch (e) {
    console.error(`Failed to fetch connections in org ${organizationId}:`, e);
    logErrorToAnalytics(
      c.env,
      "CONNECTIONS_FETCH_ERROR",
      `Failed to fetch connections`,
      {
        organizationId,

        error: String(e),
      }
    );
    throw new HTTPException(500, {
      message: "Failed to fetch connections",
    });
  }
};
