"use client";
import React from "react";
import { useState } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  Card<PERSON>ooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Textarea } from "@/components/ui/textarea";
import { toast } from "@/hooks/use-toast";
import { Button } from "@/components/ui/button";
import { updateFeed } from "@/lib/actions/updateFeed";
import { updateConnection } from "@/lib/actions/updateConnection";
import { ConnectionStatus } from "@repo/api/src/types";

export const BasicConnectionSettings = ({
  projectId,
  connectionId,
  name: currentName,
  status,
}: {
  projectId: string;
  connectionId: string;
  name: string;
  status: ConnectionStatus;
}) => {
  console.log("CURRENT STATUS", status);
  const [name, setName] = useState(currentName);
  const [isActive, setIsActive] = useState(
    status === "active" || status === "auth_needed" || status === "expired"
  );
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const handleSaveSettings = async () => {
    await updateConnection({
      name: name,
      isActive,
      projectId: projectId,
      connectionId: connectionId,
    });
    setHasUnsavedChanges(false);
    toast({
      title: "Connection updated",
      description: "Connection configuration has been saved successfully.",
    });
  };
  const handleInputChange =
    (setter: (value: string) => void) => (value: string) => {
      setter(value);
      setHasUnsavedChanges(true);
    };

  const handleStatusToggle = (checked: boolean) => {
    setIsActive(checked);
    setHasUnsavedChanges(true);
  };
  return (
    <Card>
      <CardHeader>
        <CardTitle>Basic Settings</CardTitle>
        <CardDescription>
          Configure the basic properties of your connection.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid gap-2">
          <Label htmlFor="name">Connection Name</Label>
          <Input
            id="name"
            value={name}
            onChange={(e) => handleInputChange(setName)(e.target.value)}
            placeholder="Enter connection name"
          />
        </div>
        <div className="flex items-center justify-between">
          <div className="space-y-0.5">
            <Label htmlFor="status">Connection Status</Label>
            <p className="text-sm text-muted-foreground">
              Enable or disable this connection
            </p>
          </div>
          <Switch
            id="status"
            checked={isActive}
            onCheckedChange={handleStatusToggle}
          />
        </div>
      </CardContent>
      <CardFooter>
        <Button disabled={!hasUnsavedChanges} onClick={handleSaveSettings}>
          Save
        </Button>
      </CardFooter>
    </Card>
  );
};
