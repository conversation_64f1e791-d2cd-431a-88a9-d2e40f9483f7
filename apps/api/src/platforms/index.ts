// src/platforms/index.ts
import { Platform, Bindings, InsertPost, PlatformConnection } from "../types";
import * as YouTube from "./youtube";
import * as Meta from "./meta";

export interface PlatformAdapter {
  checkToken(accessToken: string, env: Bindings): Promise<boolean>;
  syncPosts(
    connection: PlatformConnection,
    accessToken: string,
    env: Bindings,
    limit?: number
  ): Promise<{
    posts: any[];
    channelMetrics?: any;
    error?: any;
  }>;
  mapPostData(post: any, connection: PlatformConnection): InsertPost;
}

class YouTubePlatformAdapter implements PlatformAdapter {
  async checkToken(accessToken: string, env: Bindings): Promise<boolean> {
    return YouTube.checkYouTubeToken(accessToken, env);
  }

  async syncPosts(
    connection: PlatformConnection,
    accessToken: string,
    env: Bindings,
    limit: number = 100
  ) {
    const [videosResult, channelResult] = await Promise.all([
      YouTube.fetchYouTubeChannelVideos(
        connection.platformAccountId!,
        accessToken,
        env,
        limit
      ),
      YouTube.fetchYouTubeChannelInfo(
        connection.platformAccountId!,
        accessToken,
        env
      ),
    ]);

    if (!videosResult) {
      throw new Error(`YouTube videos API client failed`);
    }
    if (videosResult.error) {
      throw videosResult.error;
    }

    const posts = videosResult.videos || [];
    let channelMetrics = null;

    if (channelResult && channelResult.channel && !channelResult.error) {
      const channel = channelResult.channel;
      channelMetrics = {
        subscriberCount: parseInt(
          channel.statistics?.subscriberCount || "0",
          10
        ),
        videoCount: parseInt(channel.statistics?.videoCount || "0", 10),
        viewCount: parseInt(channel.statistics?.viewCount || "0", 10),
        lastUpdated: new Date().toISOString(),
      };
    }

    return { posts, channelMetrics };
  }

  mapPostData(post: any, connection: PlatformConnection): InsertPost {
    return {
      platformConnectionId: connection.id,
      mediaId: post.id,
      likeCount: parseInt(post.statistics?.likeCount || "0", 10),
      commentsCount: parseInt(post.statistics?.commentCount || "0", 10),
      caption: post.snippet?.title || "",
      mediaUrl: `https://www.youtube.com/watch?v=${post.id}`,
      mediaType: "VIDEO",
      permalink: `https://www.youtube.com/watch?v=${post.id}`,
      timestamp: new Date(post.snippet?.publishedAt),
      lastFetched: new Date(),
    };
  }
}

class MetaPlatformAdapter implements PlatformAdapter {
  async checkToken(accessToken: string, env: Bindings): Promise<boolean> {
    const testUrl = `https://graph.facebook.com/v18.0/me?fields=id&access_token=${accessToken}`;
    try {
      const response = await fetch(testUrl);
      if (!response.ok) return false;
      const jsonResp = (await response.json()) as any;
      return jsonResp.id ? true : false;
    } catch {
      return false;
    }
  }

  async syncPosts(
    connection: PlatformConnection,
    accessToken: string,
    env: Bindings,
    limit: number = 100
  ) {
    const mediaResult = await Meta.fetchLatestGraphMedia(
      accessToken,
      connection.platformAccountId!,
      env,
      limit
    );

    if (!mediaResult) {
      throw new Error(`Meta API client failed`);
    }
    if (mediaResult.error) {
      throw mediaResult.error;
    }

    return { posts: mediaResult.posts || [] };
  }

  mapPostData(post: any, connection: PlatformConnection): InsertPost {
    return {
      platformConnectionId: connection.id,
      mediaId: post.id,
      likeCount: (post as any).like_count ?? 0,
      commentsCount: (post as any).comments_count ?? 0,
      caption: post.caption,
      mediaUrl: post.media_url,
      mediaType: post.media_type,
      permalink: post.permalink,
      timestamp: new Date(post.timestamp),
      lastFetched: new Date(),
    };
  }
}

class BasicDisplayPlatformAdapter implements PlatformAdapter {
  async checkToken(accessToken: string, env: Bindings): Promise<boolean> {
    return Meta.checkBasicDisplayToken(accessToken, env);
  }

  async syncPosts(
    connection: PlatformConnection,
    accessToken: string,
    env: Bindings,
    limit: number = 50
  ) {
    const mediaResult = await Meta.fetchBasicDisplayMedia(
      accessToken,
      env,
      limit
    );

    if (!mediaResult) {
      throw new Error(`Basic Display API client failed`);
    }
    if (mediaResult.error) {
      throw mediaResult.error;
    }

    return { posts: mediaResult.posts || [] };
  }

  mapPostData(post: any, connection: PlatformConnection): InsertPost {
    return {
      platformConnectionId: connection.id,
      mediaId: post.id,
      likeCount: 0, // Basic Display doesn't provide like counts
      commentsCount: 0, // Basic Display doesn't provide comment counts
      caption: post.caption,
      mediaUrl: post.media_url,
      mediaType: post.media_type,
      permalink: post.permalink,
      timestamp: new Date(post.timestamp),
      lastFetched: new Date(),
    };
  }
}

// Platform Registry
const platformAdapters: Record<Platform, PlatformAdapter> = {
  youtube: new YouTubePlatformAdapter(),
  instagram_business: new MetaPlatformAdapter(),
  facebook: new MetaPlatformAdapter(),
  tiktok: new MetaPlatformAdapter(), // Placeholder for future TikTok implementation
  instagram: new BasicDisplayPlatformAdapter(),
};

export function getPlatformAdapter(platform: Platform): PlatformAdapter {
  const adapter = platformAdapters[platform];
  if (!adapter) {
    throw new Error(`No adapter found for platform: ${platform}`);
  }
  return adapter;
}

// Re-export platform-specific functions for backward compatibility
export { YouTube, Meta };
