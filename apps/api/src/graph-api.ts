// src/graph-api.ts
import { z } from "zod";
import {
  ApiFetchResult,
  Bindings,
  GraphApiMedia,
  GraphApiMediaSchema,
  GraphApiCommentsResponseSchema,
  BasicDisplayApiResponseSchema,
  BatchApiResponseItemSchema,
  GraphApiComment,
  BasicDisplayApiMediaItem,
  GraphApiMeSchema,
  GraphApiCommentsResponse,
  GraphApiPagingSchema,
  YouTubeChannelResponseSchema,
  YouTubeVideosResponseSchema,
} from "./types";
import { logErrorToAnalytics } from "./analytics-utils"; // Import error logger

import * as constants from "./constants";

// --- Fehlerklasse ---
export class GraphApiError extends Error {
  constructor(
    message: string,
    public status?: number,
    public isRetryable: boolean = true,
    public isAuthError: boolean = false
  ) {
    super(message);
    this.name = "GraphApiError";
  }
}

// --- Retry Helper ---
async function fetchWithRetry(
  url: string,
  options: RequestInit = {},
  contextInfo: string,
  env: Bindings
): Promise<Response> {
  let attempts = 0;
  let delay = constants.INITIAL_RETRY_DELAY_MS;

  while (attempts < constants.MAX_API_RETRIES) {
    attempts++;
    try {
      const response = await fetch(url, options);
      if (response.ok) return response;

      const status = response.status;
      // Versuche Body zu lesen für Fehlerdetails
      let errorBodyText: string | null = null;
      try {
        errorBodyText = await response.clone().text();
      } catch {
        /* ignore */
      }

      console.error(
        `GRAPH_API: Error (Attempt ${attempts}, Status ${status}) for ${contextInfo}:`,
        errorBodyText ?? "(no body)"
      );

      if (status >= 400 && status < 500 && status !== 429) {
        const isAuth =
          [401, 403].includes(status) ||
          (status === 400 && errorBodyText?.includes("OAuthException"));
        throw new GraphApiError(
          `Non-retryable API error ${status}`,
          status,
          false,
          isAuth
        );
      }
      if (attempts >= constants.MAX_API_RETRIES) {
        throw new GraphApiError(
          `API call failed after ${attempts} attempts (Status ${status})`,
          status,
          false
        );
      }
    } catch (error: any) {
      console.error(
        `GRAPH_API: Network/Fetch Error (Attempt ${attempts}) for ${contextInfo}:`,
        error
      );
      if (error instanceof GraphApiError) throw error;
      if (attempts >= constants.MAX_API_RETRIES) {
        throw new GraphApiError(
          `API call failed after ${attempts} attempts (Network/Fetch Error)`,
          undefined,
          false
        );
      }
    }
    console.log(
      `GRAPH_API: Waiting ${delay}ms before next retry for ${contextInfo}`
    );
    await new Promise((resolve) => setTimeout(resolve, delay));
    delay *= 2;
  }
  throw new GraphApiError(
    "API call failed unexpectedly after max retries",
    undefined,
    false
  );
}

// --- Graph API Funktionen ---

export async function fetchMediaDataAndFirstComments(
  mediaId: string,
  accessToken: string,
  env: Bindings
): Promise<ApiFetchResult | null> {
  const contextInfo = `Media ${mediaId}`;
  let baseData: GraphApiMedia | undefined;
  let fetchedComments: GraphApiComment[] = [];
  let nextPageUrl: string | null = null;
  try {
    // 1. Fetch Base Media Data
    const baseFields =
      "id,caption,like_count,comments_count,media_url,timestamp,username,media_type,permalink,thumbnail_url,children{id,media_url,media_type}";
    const baseUrl = `https://graph.facebook.com/${constants.META_API_VERSION}/${mediaId}?fields=${baseFields}&access_token=${accessToken}`;
    const baseResponse = await fetchWithRetry(
      baseUrl,
      {},
      `${contextInfo} Base`,
      env
    );
    const baseJson = await baseResponse.json();
    const baseValidation = GraphApiMediaSchema.safeParse(baseJson);
    if (!baseValidation.success) {
      logErrorToAnalytics(
        env,
        "API_VALIDATION_ERROR",
        `Invalid base media data structure for ${mediaId}`,
        {
          context: contextInfo,
          errors: baseValidation.error.flatten(),
          received: baseJson,
        }
      );
      throw new GraphApiError(
        "Invalid base API response structure",
        baseResponse.status,
        false
      );
    }
    baseData = baseValidation.data;

    // 2. Fetch First Comments Page
    const commentsFields = "id,text,timestamp,from{id,username}";
    const commentsUrl = `https://graph.facebook.com/${constants.META_API_VERSION}/${mediaId}/comments?fields=${commentsFields}&limit=25&access_token=${accessToken}`;
    try {
      // Separate try-catch for comments, base data might still be useful
      const commentsResponse = await fetchWithRetry(
        commentsUrl,
        {},
        `${contextInfo} Comments P1`,
        env
      );
      const commentsJson = await commentsResponse.json();
      const commentsValidation =
        GraphApiCommentsResponseSchema.safeParse(commentsJson);
      if (commentsValidation.success) {
        fetchedComments = commentsValidation.data.data || [];
        nextPageUrl = commentsValidation.data.paging?.next || null;
      } else {
        logErrorToAnalytics(
          env,
          "API_VALIDATION_ERROR",
          `Invalid comments page structure for ${mediaId}`,
          {
            context: contextInfo,
            errors: commentsValidation.error.flatten(),
            received: commentsJson,
          }
        );
        // Continue without comments data
      }
    } catch (commentsError) {
      console.error(
        `GRAPH_API: Failed to fetch comments page 1 for ${contextInfo}:`,
        commentsError
      );
      // Log error, but maybe return base data anyway
      logErrorToAnalytics(
        env,
        "API_FETCH_ERROR",
        `Failed fetching comments page 1`,
        { context: contextInfo, error: String(commentsError) }
      );
    }

    return { baseData, fetchedComments, nextPageUrl };
  } catch (error: any) {
    console.error(
      `GRAPH_API: Failed to fetch data for ${contextInfo}:`,
      error.message
    );
    // Logge den Hauptfehler
    logErrorToAnalytics(
      env,
      "API_FETCH_ERROR",
      `Failed fetching base media data`,
      { context: contextInfo, error: String(error) }
    );
    if (error instanceof GraphApiError && error.isAuthError) throw error; // Auth Fehler weiterleiten
    return null;
  }
}

export async function fetchNextCommentPage(
  nextPageUrl: string,
  env: Bindings
): Promise<{ comments: GraphApiComment[]; nextPageUrl: string | null } | null> {
  const contextInfo = `Comments Pagination ${nextPageUrl.substring(0, 50)}...`;
  try {
    const response = await fetchWithRetry(nextPageUrl, {}, contextInfo, env);
    const pageJson = await response.json();
    const validationResult = GraphApiCommentsResponseSchema.safeParse(pageJson);
    if (!validationResult.success) {
      logErrorToAnalytics(
        env,
        "API_VALIDATION_ERROR",
        `Invalid pagination response structure`,
        {
          context: contextInfo,
          errors: validationResult.error.flatten(),
          received: pageJson,
        }
      );
      throw new GraphApiError(
        "Invalid pagination API response structure",
        response.status,
        false
      );
    }
    const pageData = validationResult.data;
    return {
      comments: pageData.data || [],
      nextPageUrl: pageData.paging?.next || null,
    };
  } catch (error: any) {
    console.error(
      `GRAPH_API: Failed to fetch comment page for ${contextInfo}:`,
      error.message
    );
    if (error instanceof GraphApiError && error.isAuthError) throw error;
    return null;
  }
}

export async function fetchBatchMediaDataAndFirstComments(
  mediaIds: string[],
  accessToken: string,
  env: Bindings
): Promise<
  | {
      mediaId: string;
      success: boolean;
      data?: ApiFetchResult;
      error?: any;
      isAuthError?: boolean;
    }[]
  | null
> {
  if (mediaIds.length === 0) return [];
  const contextInfo = `Batch Fetch (${mediaIds.length} items)`;
  let attempts = 0;
  let delay = constants.INITIAL_RETRY_DELAY_MS;
  const baseFields =
    "id,caption,like_count,comments_count,media_url,timestamp,username,media_type,permalink,thumbnail_url,children{id,media_url,media_type}";
  const commentsFields = "id,text,timestamp,from{id,username}";
  const batchPayload: { method: string; relative_url: string }[] = [];
  for (const id of mediaIds) {
    batchPayload.push({
      method: "GET",
      relative_url: `${constants.META_API_VERSION}/${id}?fields=${baseFields}`,
    });
    batchPayload.push({
      method: "GET",
      relative_url: `${constants.META_API_VERSION}/${id}/comments?fields=${commentsFields}&limit=25`,
    });
  }

  while (attempts < constants.MAX_API_RETRIES) {
    attempts++;
    console.log(
      `GRAPH_API: Batch attempt ${attempts} for ${mediaIds.length} items.`
    );
    try {
      const url = `https://graph.facebook.com/${constants.META_API_VERSION}/`;
      const body = new URLSearchParams();
      body.append("access_token", accessToken);
      body.append("batch", JSON.stringify(batchPayload));
      const response = await fetch(url, { method: "POST", body: body });

      if (!response.ok) {
        /* ... Fehlerbehandlung mit Retry/Throw wie oben ... */
      }

      const resultsJson = await response.json();
      const resultsValidation = z
        .array(BatchApiResponseItemSchema)
        .safeParse(resultsJson); // Validiere Gesamtstruktur
      if (
        !resultsValidation.success ||
        resultsValidation.data.length !== batchPayload.length
      ) {
        logErrorToAnalytics(
          env,
          "API_VALIDATION_ERROR",
          "Invalid Batch API response structure or length mismatch",
          {
            context: contextInfo,
            errors: resultsValidation.error?.flatten(),
            receivedCount: Array.isArray(resultsJson)
              ? resultsJson.length
              : "N/A",
            expectedCount: batchPayload.length,
          }
        );
        throw new GraphApiError(
          "Unexpected Batch API response format",
          undefined,
          false
        );
      }
      const results = resultsValidation.data;

      // Verarbeite Batch Ergebnisse mit Zod Validierung für jeden Body
      const processedResults: {
        mediaId: string;
        success: boolean;
        data?: ApiFetchResult;
        error?: any;
        isAuthError?: boolean;
      }[] = [];
      const resultMap: {
        [key: string]: {
          baseData?: GraphApiMedia;
          commentsResponse?: GraphApiCommentsResponse;
          baseError?: any;
          commentsError?: any;
          isAuthError?: boolean;
        };
      } = {};

      results.forEach((res, index) => {
        const originalRequest = batchPayload[index];
        const urlMatch =
          originalRequest.relative_url.match(/v\d+\.\d+\/([^/?]+)/);
        if (!urlMatch || !urlMatch[1]) return;
        const mediaId = urlMatch[1];
        if (!resultMap[mediaId]) resultMap[mediaId] = {};
        const code = res.code;
        let bodyData = null;
        let parseError = null;
        try {
          bodyData = JSON.parse(res.body);
        } catch (e) {
          parseError = e;
        }

        if (code >= 200 && code < 300 && bodyData) {
          if (originalRequest.relative_url.includes("/comments")) {
            const commentsValidation =
              GraphApiCommentsResponseSchema.safeParse(bodyData);
            if (commentsValidation.success)
              resultMap[mediaId].commentsResponse = commentsValidation.data;
            else {
              resultMap[mediaId].commentsError = "Invalid comments structure";
              logErrorToAnalytics(
                env,
                "API_VALIDATION_ERROR",
                "Invalid comments structure in batch",
                { mediaId, errors: commentsValidation.error.flatten() }
              );
            }
          } else {
            const mediaValidation = GraphApiMediaSchema.safeParse(bodyData);
            if (mediaValidation.success)
              resultMap[mediaId].baseData = mediaValidation.data;
            else {
              resultMap[mediaId].baseError = "Invalid media structure";
              logErrorToAnalytics(
                env,
                "API_VALIDATION_ERROR",
                "Invalid media structure in batch",
                { mediaId, errors: mediaValidation.error.flatten() }
              );
            }
          }
        } else {
          const isSubAuthError = [400, 401, 403].includes(code);
          const errorPayload =
            bodyData?.error?.message ||
            parseError ||
            res.body ||
            `Error Code ${code}`;
          console.error(
            `GRAPH_API: Error in Batch sub-request for ${originalRequest.relative_url} (Code: ${code}):`,
            errorPayload
          );
          if (originalRequest.relative_url.includes("/comments"))
            resultMap[mediaId].commentsError = errorPayload;
          else resultMap[mediaId].baseError = errorPayload;
          if (isSubAuthError) resultMap[mediaId].isAuthError = true;
        }
      });
      // Konsolidiere Ergebnisse (wie zuvor)
      for (const mediaId of mediaIds) {
        /* ... */
      }
      return processedResults;
    } catch (error: any) {
      /* ... Fehler behandeln, Auth prüfen, Retry ... */
    }
  }
  return null;
}

// --- Basic Display API Funktionen ---

export async function fetchBasicDisplayMedia(
  accessToken: string,
  env: Bindings,
  limit: number = 25
): Promise<{ posts: BasicDisplayApiMediaItem[]; error?: any } | null> {
  const contextInfo = `Basic Display Media`;
  const fields =
    "id,caption,media_type,media_url,permalink,thumbnail_url,timestamp,username";
  let url: string | null =
    `https://graph.instagram.com/${constants.BASIC_DISPLAY_API_VERSION}/me/media?fields=${fields}&limit=${limit}&access_token=${accessToken}`;
  try {
    const response = await fetch(url); // Einfacher Fetch hier
    if (!response.ok) {
      const errorText = await response.text();
      const isAuth = [400, 401, 403].includes(response.status);
      console.error(
        `GRAPH_API (Basic): Error fetching media: ${response.status}`,
        errorText
      );
      throw new GraphApiError(
        `Basic Display API error ${response.status}`,
        response.status,
        false,
        isAuth
      );
    }
    const pageJson = await response.json();
    const validationResult = BasicDisplayApiResponseSchema.safeParse(pageJson);
    if (!validationResult.success) {
      logErrorToAnalytics(
        env,
        "API_VALIDATION_ERROR",
        "Invalid Basic Display API response",
        {
          context: contextInfo,
          errors: validationResult.error.flatten(),
          received: pageJson,
        }
      );
      throw new GraphApiError(
        "Invalid Basic Display API response structure",
        response.status,
        false
      );
    }
    return { posts: validationResult.data.data || [] };
  } catch (error: any) {
    console.error(`GRAPH_API (Basic): Failed to fetch latest media:`, error);
    logErrorToAnalytics(
      env,
      "BASIC_DISPLAY_FETCH_ERROR",
      "Failed fetching basic display media",
      { error: String(error) }
    );
    return {
      posts: [],
      error:
        error instanceof GraphApiError
          ? error
          : new GraphApiError(
              String(error),
              undefined,
              false,
              error.isAuthError
            ),
    };
  }
}

export async function checkBasicDisplayToken(
  accessToken: string,
  env: Bindings
): Promise<boolean> {
  const url = `https://graph.instagram.com/${constants.BASIC_DISPLAY_API_VERSION}/me?fields=id&access_token=${accessToken}`;
  try {
    const response = await fetch(url);
    if (!response.ok) return false;
    const jsonResp = await response.json();
    const validation = GraphApiMeSchema.safeParse(jsonResp);
    if (!validation.success) {
      logErrorToAnalytics(
        env,
        "API_VALIDATION_ERROR",
        "Invalid Basic Display /me response",
        { errors: validation.error.flatten(), received: jsonResp }
      );
      return false;
    }
    return true;
  } catch (e) {
    console.error("Basic Display token check failed:", e);
    return false;
  }
}

// --- NEU: Funktion für den Sync - Holt die letzten Posts via Graph API ---
export async function fetchLatestGraphMedia(
  accessToken: string,
  platformAccountId: string, // IG User ID oder FB Page ID
  env: Bindings,
  limit: number = 100
): Promise<{ posts: GraphApiMedia[]; error?: any } | null> {
  const contextInfo = `Latest Graph Media for ${platformAccountId}`;
  const collectedPosts: GraphApiMedia[] = [];
  let fetchLimitPerPage = Math.min(limit, 50); // Beispiel: Max 50 pro Seite
  const fields =
    "id,caption,like_count,comments_count,media_url,timestamp,username,media_type,permalink,thumbnail_url,children{id,media_url,media_type}";
  let url: string | null =
    `https://graph.facebook.com/${constants.META_API_VERSION}/${platformAccountId}/media?fields=${fields}&limit=${fetchLimitPerPage}&access_token=${accessToken}`;

  try {
    while (url && collectedPosts.length < limit) {
      const remainingLimit = limit - collectedPosts.length;
      const currentLimit = Math.min(fetchLimitPerPage, remainingLimit);
      const urlObj = new URL(url);
      urlObj.searchParams.set("limit", currentLimit.toString());
      url = urlObj.toString();

      console.log(
        `GRAPH_API: Fetching graph media page for ${platformAccountId}...`
      );
      const response = await fetchWithRetry(
        url,
        {},
        `${contextInfo} Page`,
        env
      ); // Nutze Retry
      const pageJson = await response.json();

      // Validiere die Antwortstruktur (erwartet { data: [], paging: {} })
      const pageValidation = z
        .object({
          data: z.array(GraphApiMediaSchema),
          paging: GraphApiPagingSchema.optional(),
        })
        .safeParse(pageJson);

      if (!pageValidation.success) {
        logErrorToAnalytics(
          env,
          "API_VALIDATION_ERROR",
          "Invalid Graph API media page structure",
          {
            context: contextInfo,
            errors: pageValidation.error.flatten(),
            received: pageJson,
          }
        );
        throw new GraphApiError(
          "Invalid API media page structure",
          response.status,
          false
        );
      }
      const pageData = pageValidation.data;

      if (pageData.data && pageData.data.length > 0) {
        collectedPosts.push(...pageData.data);
      } else {
        break;
      } // Keine Daten mehr
      if (collectedPosts.length >= limit) break; // Limit erreicht

      url = pageData.paging?.next || null; // Nächste Seite
      if (url)
        console.log(
          `GRAPH_API: Fetching next graph media page for ${platformAccountId}...`
        );
    }
    console.log(
      `GRAPH_API: Fetched ${collectedPosts.length} graph posts for ${platformAccountId}.`
    );
    return { posts: collectedPosts.slice(0, limit) };
  } catch (error: any) {
    console.error(
      `GRAPH_API: Failed to fetch latest graph media for ${platformAccountId}:`,
      error
    );
    logErrorToAnalytics(
      env,
      "GRAPH_MEDIA_FETCH_ERROR",
      "Failed fetching graph media",
      { platformAccountId, error: String(error) }
    );
    return {
      posts: [],
      error:
        error instanceof GraphApiError
          ? error
          : new GraphApiError(
              String(error),
              undefined,
              false,
              error.isAuthError
            ),
    };
  }
}

// --- YouTube API Functions ---

/**
 * Fetches channel information including subscriber count
 */
export async function fetchYouTubeChannelInfo(
  channelId: string,
  accessToken: string,
  env: Bindings
): Promise<{ channel: any; error?: any } | null> {
  const contextInfo = `YouTube Channel ${channelId}`;
  try {
    const url = `https://www.googleapis.com/youtube/v3/channels?part=snippet,statistics&id=${channelId}&access_token=${accessToken}`;

    const response = await fetchWithRetry(url, {}, `${contextInfo} Info`, env);

    const jsonResp = await response.json();
    const validation = YouTubeChannelResponseSchema.safeParse(jsonResp);

    if (!validation.success) {
      logErrorToAnalytics(
        env,
        "YOUTUBE_CHANNEL_VALIDATION_ERROR",
        "Invalid YouTube channel response",
        { errors: validation.error.flatten(), received: jsonResp }
      );
      return null;
    }

    if (validation.data.items.length === 0) {
      console.warn(`YouTube: No channel found for ID ${channelId}`);
      return null;
    }

    return { channel: validation.data.items[0] };
  } catch (error: any) {
    console.error(
      `YouTube: Failed to fetch channel info for ${channelId}:`,
      error
    );
    logErrorToAnalytics(
      env,
      "YOUTUBE_CHANNEL_FETCH_ERROR",
      "Failed fetching YouTube channel",
      { channelId, error: String(error) }
    );
    return { channel: null, error };
  }
}

/**
 * Fetches videos from a YouTube channel
 */
export async function fetchYouTubeChannelVideos(
  channelId: string,
  accessToken: string,
  env: Bindings,
  limit: number = 50
): Promise<{ videos: any[]; error?: any } | null> {
  const contextInfo = `YouTube Videos for ${channelId}`;
  const collectedVideos: any[] = [];
  let pageToken: string | undefined;

  try {
    while (collectedVideos.length < limit) {
      const remainingLimit = limit - collectedVideos.length;
      const currentLimit = Math.min(remainingLimit, 50); // YouTube API max per request

      let url = `https://www.googleapis.com/youtube/v3/search?part=id&channelId=${channelId}&type=video&order=date&maxResults=${currentLimit}&access_token=${accessToken}`;
      if (pageToken) {
        url += `&pageToken=${pageToken}`;
      }

      console.log(`YouTube: Fetching videos page for ${channelId}...`);
      const searchResponse = await fetchWithRetry(
        url,
        {},
        `${contextInfo} Search`,
        env
      );

      const searchJson = (await searchResponse.json()) as any;

      if (!searchJson.items || searchJson.items.length === 0) {
        break;
      }

      // Get video IDs
      const videoIds = searchJson.items
        .map((item: any) => item.id.videoId)
        .join(",");

      // Fetch detailed video information
      const detailsUrl = `https://www.googleapis.com/youtube/v3/videos?part=snippet,statistics&id=${videoIds}&access_token=${accessToken}`;
      const detailsResponse = await fetchWithRetry(
        detailsUrl,
        {},
        `${contextInfo} Details`,
        env
      );

      const detailsJson = await detailsResponse.json();
      const validation = YouTubeVideosResponseSchema.safeParse(detailsJson);

      if (!validation.success) {
        logErrorToAnalytics(
          env,
          "YOUTUBE_VIDEOS_VALIDATION_ERROR",
          "Invalid YouTube videos response",
          { errors: validation.error.flatten(), received: detailsJson }
        );
        break;
      }

      collectedVideos.push(...validation.data.items);

      pageToken = searchJson.nextPageToken;
      if (!pageToken || collectedVideos.length >= limit) {
        break;
      }
    }

    console.log(
      `YouTube: Fetched ${collectedVideos.length} videos for ${channelId}.`
    );
    return { videos: collectedVideos.slice(0, limit) };
  } catch (error: any) {
    console.error(`YouTube: Failed to fetch videos for ${channelId}:`, error);
    logErrorToAnalytics(
      env,
      "YOUTUBE_VIDEOS_FETCH_ERROR",
      "Failed fetching YouTube videos",
      { channelId, error: String(error) }
    );
    return { videos: [], error };
  }
}

/**
 * Checks if a YouTube access token is valid
 */
export async function checkYouTubeToken(
  accessToken: string,
  env: Bindings
): Promise<boolean> {
  const url = `https://www.googleapis.com/youtube/v3/channels?part=id&mine=true&access_token=${accessToken}`;
  try {
    const response = await fetch(url);
    if (!response.ok) return false;

    const jsonResp = (await response.json()) as any;
    return jsonResp.items && jsonResp.items.length > 0;
  } catch (e) {
    console.error("YouTube token check failed:", e);
    return false;
  }
}
