import { Context } from "hono";
import { AppContext } from "../../../../types";
import { HTTPException } from "hono/http-exception";
import { getDbClient } from "../../../../database-service";
import * as schema from "@repo/drizzle-schema/d1";
import { logErrorToAnalytics } from "../../../../analytics-utils";
import { fixUserAndOrgTable } from "../../../manage/account/fix";
import { DateTime } from "luxon";
import { zValidator } from "@hono/zod-validator";
import { z } from "zod";
import { and, eq } from "drizzle-orm";

const validator = z.object({
  name: z.string().optional().nullable(),
  description: z.string().optional().nullable(),
  isActive: z.boolean().optional().nullable(),
  connections: z.array(z.string()).optional().nullable(),
});

export const updateFeedValidator = zValidator("json", validator);

export const updateFeed = async (
  c: Context<
    AppContext,
    "/manage/projects/:projectId/feeds/:feedId",
    {
      in: {
        json: z.infer<typeof validator>;
      };
      out: {
        json: z.infer<typeof validator>;
      };
    }
  >
) => {
  const organizationId = c.var.organizationId;
  if (!organizationId)
    throw new HTTPException(403, {
      message: "Organization context required",
    });

  const validated = c.req.valid("json");

  const name = validated.name;
  const description = validated.description;
  const isActive = validated.isActive;
  const connections = validated.connections;
  const userId = c.var.user?.userId;

  if (!userId)
    throw new HTTPException(400, { message: "You must be logged in" });

  const projectId = c.req.param("projectId");
  const feedId = c.req.param("feedId");

  const db = getDbClient(c.env.DB);
  try {
    const valid = await db
      .select({
        id: schema.feeds.id,
      })
      .from(schema.feeds)
      .innerJoin(
        schema.projects,
        eq(schema.feeds.projectId, schema.projects.id)
      )
      .where(
        and(
          eq(schema.feeds.id, feedId),
          eq(schema.projects.id, projectId),
          eq(schema.projects.organizationId, organizationId)
        )
      )
      .get();

    if (!valid) {
      throw new HTTPException(400, { message: "Project not found" });
    }

    await db
      .update(schema.feeds)
      .set({
        ...(name && { name }),
        ...(description && { description }),
        ...(isActive !== null &&
          isActive !== undefined && { isActive: isActive }),
        updatedAt: DateTime.now().toJSDate(),
      })
      .where(eq(schema.feeds.id, feedId))
      .get();

    if (connections !== null && connections !== undefined) {
      const currentConnectionsRaw = await db
        .select({
          platformConnectionId: schema.feedConnections.platformConnectionId,
        })
        .from(schema.feedConnections)
        .where(eq(schema.feedConnections.feedId, feedId))
        .all();

      const currentConnections = currentConnectionsRaw.map(
        (conn) => conn.platformConnectionId
      );

      const connectionsToRemove = currentConnections.filter(
        (conn) => !connections.includes(conn)
      );
      const connectionsToAdd = connections.filter(
        (conn) => !currentConnections.includes(conn)
      );

      connectionsToRemove.forEach(async (connection) => {
        await db
          .delete(schema.feedConnections)
          .where(
            and(
              eq(schema.feedConnections.feedId, feedId),
              eq(schema.feedConnections.platformConnectionId, connection)
            )
          );
      });
      connectionsToAdd.forEach(async (connection) => {
        await db.insert(schema.feedConnections).values({
          feedId: feedId,
          platformConnectionId: connection,
        });
      });
    }

    return c.json({
      id: feedId,
    });
  } catch (e) {
    console.error(
      `Failed to update feed ${feedId} on project ${projectId} for org ${organizationId}:`,
      e
    );
    logErrorToAnalytics(c.env, "FEED_UPDATE_ERROR", `Failed to update feed`, {
      feedId,
      projectId,
      organizationId,
      error: String(e),
    });
    await fixUserAndOrgTable({ user: c.var.user, d1: c.env.DB });

    throw new HTTPException(500, {
      message: "Failed to update feed",
    });
  }
};
