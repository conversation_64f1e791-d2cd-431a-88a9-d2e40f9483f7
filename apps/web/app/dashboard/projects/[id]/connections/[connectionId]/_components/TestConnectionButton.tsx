"use client";

import React, { useState } from "react";
import { Button } from "@/components/ui/button";
import { RefreshCw, TestTube } from "lucide-react";
import { toast } from "@/hooks/use-toast";

const TestConnectionButton: React.FC<{
  disabled?: boolean;
  platform: string;
  connectionId: string;
}> = ({ disabled, platform, connectionId }) => {
  const [isTestingConnection, setIsTestingConnection] = useState(false);

  const handleTestConnection = async () => {
    setIsTestingConnection(true);
    await new Promise((resolve) => setTimeout(resolve, 2000));
    setIsTestingConnection(false);
    toast({
      title: "Connection tested",
      description: `${platform} connection is working properly.`,
    });
  };

  return (
    <Button
      variant="outline"
      onClick={handleTestConnection}
      disabled={isTestingConnection || disabled}
    >
      {isTestingConnection ? (
        <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
      ) : (
        <TestTube className="h-4 w-4 mr-2" />
      )}
      Test Connection
    </Button>
  );
};

export default TestConnectionButton;
