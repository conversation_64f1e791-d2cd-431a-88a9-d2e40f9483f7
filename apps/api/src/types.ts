// src/types.ts
import { z } from "zod";
import { InferSelectModel, InferInsertModel } from "drizzle-orm";
import * as schema from "./db/schema";
import { Hono } from "hono";
import * as jose from "jose";
import { OrgMemberInfo, User as PropelUser } from "@propelauth/node";

// --- Basis Typen ---
export type Platform =
  | "instagram"
  | "instagram_business"
  | "facebook"
  | "tiktok";
export type ApiType = "graph" | "basic_display";
export type DefaultStatus = "active" | "inactive";
export type ConnectionStatus =
  | DefaultStatus
  | "auth_needed"
  | "expired"
  | "error";

// --- Utility Functions ---
/**
 * Determines the API type based on the platform.
 * Instagram uses Basic Display API, Instagram Business uses Graph API.
 */
export function getApiTypeForPlatform(platform: Platform): ApiType {
  switch (platform) {
    case "instagram":
      return "basic_display";
    case "instagram_business":
    case "facebook":
      return "graph";
    case "tiktok":
      return "graph"; // Assuming TikTok uses graph-like API
    default:
      return "graph";
  }
}
export type ApiKeyStatus = DefaultStatus | "revoked";
export type WebhookCommentVerb =
  | "add"
  | "edit"
  | "edited"
  | "remove"
  | "delete"
  | "deleted";

// --- Datenbank Modell Typen ---
export type User = InferSelectModel<typeof schema.users>;
export type InsertUser = InferInsertModel<typeof schema.users>;
export type Project = InferSelectModel<typeof schema.projects>;
export type InsertProject = InferInsertModel<typeof schema.projects>;
export type Feed = InferSelectModel<typeof schema.feeds>;
export type InsertFeed = InferInsertModel<typeof schema.feeds>;
export type PlatformConnection = InferSelectModel<
  typeof schema.platformConnections
>;
export type InsertPlatformConnection = InferInsertModel<
  typeof schema.platformConnections
>;
export type ApiKey = InferSelectModel<typeof schema.apiKeys>;
export type InsertApiKey = InferInsertModel<typeof schema.apiKeys>;
export type Post = InferSelectModel<typeof schema.posts>;
export type InsertPost = InferInsertModel<typeof schema.posts>;

// --- Webhook & Verarbeitungstypen ---
export interface WebhookChangeValue {
  media_id?: string;
  comment_id?: string;
  text?: string;
  verb?: WebhookCommentVerb;
  user_id?: string; // Platform User ID für Deauth
  [key: string]: any; // Erlaube zusätzliche Felder
}
export interface WebhookChange {
  field: string;
  value: WebhookChangeValue;
}
export interface WebhookEntry {
  id: string;
  time: number;
  changes: WebhookChange[];
}
export interface InstagramWebhookPayload {
  object: "instagram";
  entry: WebhookEntry[];
}
export interface UserDeauthorizationPayload {
  object: "user";
  entry: WebhookEntry[];
}

export interface WebhookSignalData {
  mediaId: string;
  field: string;
  verb?: WebhookCommentVerb;
  commentId?: string;
}

// State im DO für Cooldown-Modell
export interface MediaCooldownStatus {
  cooldownUntil?: number;
  needsFetchAfterCooldown?: boolean;
  // Optional: actions?: { verb: WebhookCommentVerb; commentId: string; }[]; // Nur wenn delete/edit actions gesammelt werden müssen
}

// Queue Message für Paginierung
export interface PaginationQueueMessage {
  platformConnectionId: string;
  mediaId: string;
  nextPageUrl: string;
}

// Queue Message für Sync
export interface SyncQueueMessage {
  platformConnectionId: string;
  triggeredByUserId?: string;
}

// --- Zod Schemas für Meta API Antworten ---
export const GraphApiErrorSchema = z.object({
  error: z.object({
    message: z.string(),
    type: z.string(),
    code: z.number(),
    fbtrace_id: z.string().optional().nullable(),
  }),
});
export const GraphApiPagingCursorsSchema = z.object({
  before: z.string().optional(),
  after: z.string().optional(),
});
export const GraphApiPagingSchema = z.object({
  cursors: GraphApiPagingCursorsSchema.optional(),
  next: z.string().url().optional().nullable(),
  previous: z.string().url().optional().nullable(),
});
export const GraphApiCommentFromSchema = z.object({
  id: z.string(),
  username: z.string().optional(),
});
export const GraphApiCommentSchema = z.object({
  id: z.string(),
  text: z.string(),
  timestamp: z.string().datetime({ offset: true }),
  from: GraphApiCommentFromSchema.optional(),
});
export type GraphApiComment = z.infer<typeof GraphApiCommentSchema>;
export const GraphApiCommentsResponseSchema = z.object({
  data: z.array(GraphApiCommentSchema),
  paging: GraphApiPagingSchema.optional(),
});
export type GraphApiCommentsResponse = z.infer<
  typeof GraphApiCommentsResponseSchema
>;
export const GraphApiMediaSchema = z.object({
  id: z.string(),
  caption: z.string().optional().nullable(),
  like_count: z.number().int().optional().nullable(),
  comments_count: z.number().int().optional().nullable(),
  media_url: z.string().url().optional().nullable(),
  timestamp: z.string().datetime({ offset: true }),
  username: z.string().optional().nullable(),
  media_type: z
    .enum(["IMAGE", "VIDEO", "CAROUSEL_ALBUM", "UNKNOWN"])
    .catch("UNKNOWN"),
  permalink: z.string().url().optional().nullable(),
  thumbnail_url: z.string().url().optional().nullable(),
  children: z
    .object({
      data: z.array(
        z.object({
          id: z.string(),
          media_url: z.string().url().optional().nullable(),
          media_type: z.string().optional().nullable(),
        })
      ),
    })
    .optional(),
});
export type GraphApiMedia = z.infer<typeof GraphApiMediaSchema>;
export const GraphApiMeSchema = z.object({ id: z.string() });
export type GraphApiMe = z.infer<typeof GraphApiMeSchema>;
export const BatchApiResponseItemSchema = z.object({
  code: z.number().int(),
  headers: z
    .array(z.object({ name: z.string(), value: z.string() }))
    .optional()
    .nullable(),
  body: z.string(),
});
export type BatchApiResponseItem = z.infer<typeof BatchApiResponseItemSchema>;
export const BasicDisplayApiMediaItemSchema = z.object({
  id: z.string(),
  caption: z.string().optional().nullable(),
  media_type: z.string(),
  media_url: z.string().url().optional().nullable(),
  permalink: z.string().url().optional().nullable(),
  thumbnail_url: z.string().url().optional().nullable(),
  timestamp: z.string().datetime({ offset: true }),
  username: z.string().optional().nullable(),
});
export type BasicDisplayApiMediaItem = z.infer<
  typeof BasicDisplayApiMediaItemSchema
>;
export const BasicDisplayApiResponseSchema = z.object({
  data: z.array(BasicDisplayApiMediaItemSchema),
  paging: GraphApiPagingSchema.optional(),
});
export type BasicDisplayApiResponse = z.infer<
  typeof BasicDisplayApiResponseSchema
>;

// --- Struktur für das Ergebnis von API-Abfragen ---
export interface ApiFetchResult {
  baseData?: GraphApiMedia;
  fetchedComments?: GraphApiComment[];
  nextPageUrl?: string | null;
}
export interface BatchApiResultItem {
  mediaId: string;
  success: boolean;
  data?: ApiFetchResult;
  error?: any;
  isAuthError?: boolean;
}

// --- Bindings & Context ---
export type Bindings = {
  DB: D1Database;
  DEBOUNCER_DO: DurableObjectNamespace;
  USAGE_ANALYTICS: AnalyticsEngineDataset;
  APP_ERRORS: AnalyticsEngineDataset;
  PAGINATION_QUEUE: Queue;
  SYNC_QUEUE: Queue;
  PAGINATION_DLQ?: Queue;
  SYNC_DLQ?: Queue;
  INSTAGRAM_APP_SECRET: string;
  YOUR_VERIFY_TOKEN: string;
  APP_ID: string;
  APP_SECRET: string;
  ENCRYPTION_KEY: string;
  JWT_SECRET?: string;
  QUEUE_MAX_RETRIES?: string;
  PROPELAUTH_AUTH_URL: string;
  PROPELAUTH_API_KEY: string;
};
export interface UserJwtPayload extends jose.JWTPayload {
  sub: string;
  name?: string;
  email?: string;
  org_id?: string;
  org_role?: string;
}
export type AppContext = {
  Bindings: Bindings; // Ihre bestehenden Bindings
  Variables: {
    // Von Propel Auth gesetzte Variablen
    user?: PropelUser;
    org?: OrgMemberInfo;
    // Ihre bestehenden Variablen
    authorizedFeedId?: string;
    apiKey?: string;
    organizationId?: string; // Kann sowohl von Propel als auch von API-Keys gesetzt werden
  };
};
export type App = Hono<AppContext>;
