import { Context } from "hono";
import { AppContext } from "../../../types";
import { HTTPException } from "hono/http-exception";
import { getDbClient } from "../../../database-service";
import * as schema from "@repo/drizzle-schema/d1";
import { logErrorToAnalytics } from "../../../analytics-utils";
import { fixUserAndOrgTable } from "../../manage/account/fix";
import { eq, inArray } from "drizzle-orm";
import { BlankInput } from "hono/types";

export const deleteProject = async (
  c: Context<AppContext, "/manage/projects/:projectId", BlankInput>
) => {
  const organizationId = c.var.organizationId;
  if (!organizationId)
    throw new HTTPException(403, {
      message: "Organization context required",
    });

  const userId = c.var.user?.userId;

  if (!userId)
    throw new HTTPException(400, { message: "Authentification failed" });

  const projectId = c.req.param("projectId");

  const db = getDbClient(c.env.DB);
  try {
    const data = await db.query.projects.findFirst({
      columns: {
        id: true,
      },
      with: {
        feeds: {
          columns: {
            id: true,
          },
        },
        platformConnections: {
          columns: {
            id: true,
          },
        },
      },
      where: (projectsTable, { eq, and }) => {
        return and(
          eq(projectsTable.id, projectId),
          eq(projectsTable.organizationId, organizationId)
        );
      },
    });

    if (!data) {
      throw new HTTPException(400, {
        message: "Project not found",
      });
    }

    await db.batch([
      db.delete(schema.apiKeys).where(
        inArray(
          schema.apiKeys.feedId,
          data.feeds.map((a) => a.id)
        )
      ),
      db.delete(schema.feeds).where(eq(schema.feeds.projectId, projectId)),
      db.delete(schema.posts).where(
        inArray(
          schema.posts.platformConnectionId,
          data.platformConnections.map((a) => a.id)
        )
      ),
      db.delete(schema.generatedLinks).where(
        inArray(
          schema.generatedLinks.platformConnectionId,
          data.platformConnections.map((a) => a.id)
        )
      ),
      db.delete(schema.feedConnections).where(
        inArray(
          schema.feedConnections.platformConnectionId,
          data.platformConnections.map((a) => a.id)
        )
      ),
      db
        .delete(schema.platformConnections)
        .where(eq(schema.platformConnections.projectId, projectId)),
      db.delete(schema.projects).where(eq(schema.projects.id, projectId)),
    ]);

    return c.json({
      success: true,
    });
  } catch (e) {
    console.error(
      `Failed to delete project ${projectId} for org ${organizationId}:`,
      e
    );
    logErrorToAnalytics(
      c.env,
      "PROJECT_DELETE_ERROR",
      `Failed to delete project`,
      {
        projectId,
        organizationId,
        error: String(e),
      }
    );
    await fixUserAndOrgTable({ user: c.var.user, d1: c.env.DB });
    throw new HTTPException(500, {
      message: "Failed to delete project",
    });
  }
};
