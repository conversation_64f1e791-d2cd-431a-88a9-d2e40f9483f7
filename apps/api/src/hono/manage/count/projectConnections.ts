import { HTTPException } from "hono/http-exception";
import { getDbClient } from "../../../database-service";
import { logErrorToAnalytics } from "../../../analytics-utils";
import { AppContext } from "../../../types";
import { Context } from "hono";
import { BlankInput } from "hono/types";
import * as schema from "@repo/drizzle-schema/d1";
import { and, eq, sql } from "drizzle-orm";

export const getProjectConnectionsCount = async (
  c: Context<
    AppContext,
    "/manage/count/projects/:projectId/connections",
    BlankInput
  >
) => {
  const projectId = c.req.param("projectId");
  const organizationId = c.var.organizationId;
  if (!organizationId) {
    throw new HTTPException(403, {
      message: "Organization context required",
    });
  }

  const db = getDbClient(c.env.DB);
  try {
    const data = await db
      .select({
        count: sql<number>`count(*)`,
        activeCount: sql<number>`count(case when platform_connections.is_active = 1 then 1 end)`,
        connectedCount: sql<number>`count(case when platform_connections.is_connected = 1 then 1 end)`,
      })
      .from(schema.platformConnections)
      .innerJoin(
        schema.projects,
        eq(schema.platformConnections.projectId, schema.projects.id)
      )
      .where(
        and(
          eq(schema.projects.organizationId, organizationId),
          eq(schema.platformConnections.projectId, projectId)
        )
      )
      .get();

    return c.json(data);
  } catch (e) {
    console.error(
      `Failed to fetch projects count for org ${organizationId}:`,
      e
    );
    logErrorToAnalytics(
      c.env,
      "PROJECT_CONNECTIONS_COUNT_ERROR",
      `Failed to count project connections`,
      {
        organizationId,
        error: String(e),
      }
    );
    throw new HTTPException(500, {
      message: "Failed to count project connections",
    });
  }
};
