"use server";

import {
  getAccessTokenAsync,
  getUser,
} from "@propelauth/nextjs/server/app-router";
import { createApiClient } from "../api";
import { getOneProjectReturnType } from "@repo/api/src/hono/manage/project/get";

export const getProject = async ({ projectId }: { projectId: string }) => {
  try {
    const accessToken = await getAccessTokenAsync();
    if (!accessToken) throw new Error("No access token");
    const user = await getUser();
    if (!user) throw new Error("No user");
    const orgId = user?.getActiveOrg()?.orgId;
    if (!orgId) throw new Error("No org id");
    const apiClient = createApiClient(accessToken, orgId);
    const res = await apiClient.manage.projects[":projectId"].$get(
      {
        param: {
          projectId,
        },
      },
      {
        init: {
          next: {
            revalidate: 60, // Daten alle 60 Sekunden revalidieren
            tags: ["project", projectId], // Cache-Tag für gezielte Revalidierung
          },
        },
      }
    );
    const project = (await res.json()) as getOneProjectReturnType;
    return project;
  } catch (e) {
    console.error("Error fetching project:", e);
    return null;
  }
};
