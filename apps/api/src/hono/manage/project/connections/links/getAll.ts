import { Context } from "hono";
import { AppContext } from "../../../../../types";
import { BlankInput } from "hono/types";
import { HTTPException } from "hono/http-exception";
import { getDbClient } from "../../../../../database-service";
import { logErrorToAnalytics } from "../../../../../analytics-utils";
import * as schema from "@repo/drizzle-schema/d1";
import { and, eq } from "drizzle-orm";

export const getAllGeneratedLinks = async (
  c: Context<
    AppContext,
    "/manage/project/:projectId/connections/:connectionId/links",
    BlankInput
  >
) => {
  console.log("ALL LINKS");
  const page = Math.min(
    Math.max(1, parseInt(c.req.query("page") || "1", 10)),
    1000
  );

  const limit = Math.min(
    Math.max(1, parseInt(c.req.query("limit") || "20", 10)),
    100
  );

  console.log("PAGEE; LIMIT", page, limit);

  const organizationId = c.var.organizationId;
  if (!organizationId)
    throw new HTTPException(403, {
      message: "Organization context required",
    });

  const projectId = c.req.param("projectId");
  const connectionId = c.req.param("connectionId");

  const db = getDbClient(c.env.DB);
  try {
    const links = await db
      .select({
        id: schema.generatedLinks.id,
        name: schema.generatedLinks.name,
        isActive: schema.generatedLinks.isActive,
        token: schema.generatedLinks.token,
        description: schema.generatedLinks.description,
        redirectUrl: schema.generatedLinks.redirectUrl,
        expiresAt: schema.generatedLinks.expiresAt,
        createdBy: schema.generatedLinks.createdBy,
        createdAt: schema.generatedLinks.createdAt,
        updatedAt: schema.generatedLinks.updatedAt,
      })
      .from(schema.generatedLinks)
      .innerJoin(
        schema.platformConnections,
        eq(
          schema.generatedLinks.platformConnectionId,
          schema.platformConnections.id
        )
      )
      .innerJoin(
        schema.projects,
        eq(schema.platformConnections.projectId, schema.projects.id)
      )
      .where(
        and(
          eq(schema.generatedLinks.platformConnectionId, connectionId),
          eq(schema.projects.organizationId, organizationId)
        )
      )
      .limit(limit)
      .offset(page ? limit * (page - 1) : 0)
      .all();

    return c.json(links);
  } catch (e) {
    console.error(
      `Failed to fetch generated links for connection ${connectionId} for project ${projectId} in org ${organizationId}:`,
      e
    );
    logErrorToAnalytics(
      c.env,
      "PROJECTS_CONNECTIONS_LINKS_FETCH_ERROR",
      `Failed to fetch generated links`,
      {
        connectionId,
        projectId,
        organizationId,
        error: String(e),
      }
    );
    throw new HTTPException(500, {
      message: "Failed to fetch generated links",
    });
  }
};
