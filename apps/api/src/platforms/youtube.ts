// src/platforms/youtube.ts
import { z } from "zod";
import {
  Bindings,
  YouTubeChannelResponseSchema,
  YouTubeVideosResponseSchema,
} from "../types";
import { logErrorToAnalytics } from "../analytics-utils";
import { fetchWithRetry } from "../graph-api-shared";

/**
 * Fetches channel information including subscriber count
 */
export async function fetchYouTubeChannelInfo(
  channelId: string,
  accessToken: string,
  env: Bindings
): Promise<{ channel: any; error?: any } | null> {
  const contextInfo = `YouTube Channel ${channelId}`;
  try {
    const url = `https://www.googleapis.com/youtube/v3/channels?part=snippet,statistics&id=${channelId}&access_token=${accessToken}`;

    const response = await fetchWithRetry(url, {}, `${contextInfo} Info`, env);

    const jsonResp = await response.json();
    const validation = YouTubeChannelResponseSchema.safeParse(jsonResp);

    if (!validation.success) {
      logErrorToAnalytics(
        env,
        "YOUTUBE_CHANNEL_VALIDATION_ERROR",
        "Invalid YouTube channel response",
        { errors: validation.error.flatten(), received: jsonResp }
      );
      return null;
    }

    if (validation.data.items.length === 0) {
      console.warn(`YouTube: No channel found for ID ${channelId}`);
      return null;
    }

    return { channel: validation.data.items[0] };
  } catch (error: any) {
    console.error(
      `YouTube: Failed to fetch channel info for ${channelId}:`,
      error
    );
    logErrorToAnalytics(
      env,
      "YOUTUBE_CHANNEL_FETCH_ERROR",
      "Failed fetching YouTube channel",
      { channelId, error: String(error) }
    );
    return { channel: null, error };
  }
}

/**
 * Fetches videos from a YouTube channel
 */
export async function fetchYouTubeChannelVideos(
  channelId: string,
  accessToken: string,
  env: Bindings,
  limit: number = 50
): Promise<{ videos: any[]; error?: any } | null> {
  const contextInfo = `YouTube Videos for ${channelId}`;
  const collectedVideos: any[] = [];
  let pageToken: string | undefined;

  try {
    while (collectedVideos.length < limit) {
      const remainingLimit = limit - collectedVideos.length;
      const currentLimit = Math.min(remainingLimit, 50); // YouTube API max per request

      let url = `https://www.googleapis.com/youtube/v3/search?part=id&channelId=${channelId}&type=video&order=date&maxResults=${currentLimit}&access_token=${accessToken}`;
      if (pageToken) {
        url += `&pageToken=${pageToken}`;
      }

      console.log(`YouTube: Fetching videos page for ${channelId}...`);
      const searchResponse = await fetchWithRetry(
        url,
        {},
        `${contextInfo} Search`,
        env
      );

      const searchJson = (await searchResponse.json()) as any;

      if (!searchJson.items || searchJson.items.length === 0) {
        break;
      }

      // Get video IDs
      const videoIds = searchJson.items
        .map((item: any) => item.id.videoId)
        .join(",");

      // Fetch detailed video information
      const detailsUrl = `https://www.googleapis.com/youtube/v3/videos?part=snippet,statistics&id=${videoIds}&access_token=${accessToken}`;
      const detailsResponse = await fetchWithRetry(
        detailsUrl,
        {},
        `${contextInfo} Details`,
        env
      );

      const detailsJson = await detailsResponse.json();
      const validation = YouTubeVideosResponseSchema.safeParse(detailsJson);

      if (!validation.success) {
        logErrorToAnalytics(
          env,
          "YOUTUBE_VIDEOS_VALIDATION_ERROR",
          "Invalid YouTube videos response",
          { errors: validation.error.flatten(), received: detailsJson }
        );
        break;
      }

      collectedVideos.push(...validation.data.items);

      pageToken = searchJson.nextPageToken;
      if (!pageToken || collectedVideos.length >= limit) {
        break;
      }
    }

    console.log(
      `YouTube: Fetched ${collectedVideos.length} videos for ${channelId}.`
    );
    return { videos: collectedVideos.slice(0, limit) };
  } catch (error: any) {
    console.error(`YouTube: Failed to fetch videos for ${channelId}:`, error);
    logErrorToAnalytics(
      env,
      "YOUTUBE_VIDEOS_FETCH_ERROR",
      "Failed fetching YouTube videos",
      { channelId, error: String(error) }
    );
    return { videos: [], error };
  }
}

/**
 * Checks if a YouTube access token is valid
 */
export async function checkYouTubeToken(
  accessToken: string,
  env: Bindings
): Promise<boolean> {
  const url = `https://www.googleapis.com/youtube/v3/channels?part=id&mine=true&access_token=${accessToken}`;
  try {
    const response = await fetch(url);
    if (!response.ok) return false;

    const jsonResp = (await response.json()) as any;
    return jsonResp.items && jsonResp.items.length > 0;
  } catch (e) {
    console.error("YouTube token check failed:", e);
    return false;
  }
}
